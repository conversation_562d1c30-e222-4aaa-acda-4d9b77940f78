# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# NextAuth Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your_nextauth_secret_32_chars_min

# JWT Configuration
JWT_SECRET=your_jwt_secret_32_chars_minimum
JWT_REFRESH_SECRET=your_jwt_refresh_secret_32_chars

# Encryption Keys
ENCRYPTION_KEY=your_32_char_encryption_key_here
AES_SECRET_KEY=your_32_char_aes_secret_key_here

# API Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Security Configuration
BCRYPT_SALT_ROUNDS=12
SESSION_TIMEOUT=3600000
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_TIME=900000

# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/securewebapp

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password

# Monitoring and Logging
LOG_LEVEL=info
ENABLE_AUDIT_LOGGING=true

# Environment
NODE_ENV=development
