-- Database Functions for Security and Audit Logging
-- These functions provide automated security monitoring and audit trails

-- Function to log audit events
CREATE OR REPLACE FUNCTION log_audit_event(
    p_user_id UUID,
    p_action VARCHAR(100),
    p_resource VARCHAR(100),
    p_resource_id UUID DEFAULT NULL,
    p_old_values JSONB DEFAULT NULL,
    p_new_values JSONB DEFAULT NULL,
    p_ip_address INET DEFAULT NULL,
    p_user_agent TEXT DEFAULT NULL,
    p_success BOOLEAN DEFAULT true,
    p_error_message TEXT DEFAULT NULL,
    p_metadata JSONB DEFAULT '{}'::JSONB
) RETURNS UUID AS $$
DECLARE
    audit_id UUID;
BEGIN
    INSERT INTO audit_logs (
        user_id, action, resource, resource_id, old_values, new_values,
        ip_address, user_agent, success, error_message, metadata
    ) VALUES (
        p_user_id, p_action, p_resource, p_resource_id, p_old_values, p_new_values,
        p_ip_address, p_user_agent, p_success, p_error_message, p_metadata
    ) RETURNING id INTO audit_id;
    
    RETURN audit_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to log security events
CREATE OR REPLACE FUNCTION log_security_event(
    p_user_id UUID,
    p_event_type VARCHAR(50),
    p_severity VARCHAR(20) DEFAULT 'medium',
    p_description TEXT,
    p_ip_address INET,
    p_user_agent TEXT DEFAULT NULL,
    p_metadata JSONB DEFAULT '{}'::JSONB
) RETURNS UUID AS $$
DECLARE
    event_id UUID;
BEGIN
    INSERT INTO security_events (
        user_id, event_type, severity, description, ip_address, user_agent, metadata
    ) VALUES (
        p_user_id, p_event_type, p_severity, p_description, p_ip_address, p_user_agent, p_metadata
    ) RETURNING id INTO event_id;
    
    RETURN event_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check account balance and limits
CREATE OR REPLACE FUNCTION check_account_limits(
    p_account_id UUID,
    p_amount DECIMAL(15,2),
    p_transaction_type transaction_type
) RETURNS JSONB AS $$
DECLARE
    account_record RECORD;
    daily_total DECIMAL(15,2);
    monthly_total DECIMAL(15,2);
    result JSONB;
BEGIN
    -- Get account details
    SELECT * INTO account_record FROM accounts WHERE id = p_account_id AND is_active = true;
    
    IF NOT FOUND THEN
        RETURN jsonb_build_object('valid', false, 'error', 'Account not found or inactive');
    END IF;
    
    -- Check if account has sufficient balance for debit transactions
    IF p_transaction_type = 'debit' THEN
        IF account_record.balance - p_amount < -account_record.overdraft_limit THEN
            RETURN jsonb_build_object('valid', false, 'error', 'Insufficient funds');
        END IF;
    END IF;
    
    -- Check daily limits for debit transactions
    IF p_transaction_type = 'debit' THEN
        SELECT COALESCE(SUM(amount), 0) INTO daily_total
        FROM transactions 
        WHERE account_id = p_account_id 
        AND transaction_type = 'debit' 
        AND status = 'completed'
        AND DATE(created_at) = CURRENT_DATE;
        
        IF daily_total + p_amount > account_record.daily_limit THEN
            RETURN jsonb_build_object('valid', false, 'error', 'Daily limit exceeded');
        END IF;
        
        -- Check monthly limits
        SELECT COALESCE(SUM(amount), 0) INTO monthly_total
        FROM transactions 
        WHERE account_id = p_account_id 
        AND transaction_type = 'debit' 
        AND status = 'completed'
        AND DATE_TRUNC('month', created_at) = DATE_TRUNC('month', CURRENT_DATE);
        
        IF monthly_total + p_amount > account_record.monthly_limit THEN
            RETURN jsonb_build_object('valid', false, 'error', 'Monthly limit exceeded');
        END IF;
    END IF;
    
    RETURN jsonb_build_object(
        'valid', true, 
        'current_balance', account_record.balance,
        'daily_remaining', account_record.daily_limit - daily_total,
        'monthly_remaining', account_record.monthly_limit - monthly_total
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to process a transaction with balance update
CREATE OR REPLACE FUNCTION process_transaction(
    p_account_id UUID,
    p_transaction_type transaction_type,
    p_amount DECIMAL(15,2),
    p_description TEXT,
    p_reference_number VARCHAR(50),
    p_external_reference VARCHAR(100) DEFAULT NULL,
    p_merchant_name VARCHAR(100) DEFAULT NULL,
    p_merchant_category VARCHAR(50) DEFAULT NULL,
    p_location JSONB DEFAULT NULL,
    p_metadata JSONB DEFAULT '{}'::JSONB
) RETURNS UUID AS $$
DECLARE
    transaction_id UUID;
    current_balance DECIMAL(15,2);
    new_balance DECIMAL(15,2);
    limit_check JSONB;
BEGIN
    -- Check account limits
    limit_check := check_account_limits(p_account_id, p_amount, p_transaction_type);
    
    IF NOT (limit_check->>'valid')::BOOLEAN THEN
        RAISE EXCEPTION 'Transaction failed: %', limit_check->>'error';
    END IF;
    
    -- Get current balance
    SELECT balance INTO current_balance FROM accounts WHERE id = p_account_id;
    
    -- Calculate new balance
    IF p_transaction_type = 'debit' THEN
        new_balance := current_balance - p_amount;
    ELSE
        new_balance := current_balance + p_amount;
    END IF;
    
    -- Insert transaction
    INSERT INTO transactions (
        account_id, transaction_type, amount, balance_after, description,
        reference_number, external_reference, merchant_name, merchant_category,
        location, metadata, status, processed_date
    ) VALUES (
        p_account_id, p_transaction_type, p_amount, new_balance, p_description,
        p_reference_number, p_external_reference, p_merchant_name, p_merchant_category,
        p_location, p_metadata, 'completed', NOW()
    ) RETURNING id INTO transaction_id;
    
    -- Update account balance
    UPDATE accounts SET balance = new_balance, updated_at = NOW() WHERE id = p_account_id;
    
    RETURN transaction_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to process internal transfer
CREATE OR REPLACE FUNCTION process_internal_transfer(
    p_from_account_id UUID,
    p_to_account_id UUID,
    p_amount DECIMAL(15,2),
    p_description TEXT,
    p_fees DECIMAL(15,2) DEFAULT 0.00
) RETURNS UUID AS $$
DECLARE
    transfer_id UUID;
    from_transaction_id UUID;
    to_transaction_id UUID;
    reference_number VARCHAR(50);
BEGIN
    -- Validate accounts are different
    IF p_from_account_id = p_to_account_id THEN
        RAISE EXCEPTION 'Cannot transfer to the same account';
    END IF;
    
    -- Generate unique reference number
    reference_number := 'TXF' || TO_CHAR(NOW(), 'YYYYMMDD') || LPAD(EXTRACT(EPOCH FROM NOW())::TEXT, 10, '0');
    
    -- Process debit transaction (from account)
    from_transaction_id := process_transaction(
        p_from_account_id,
        'debit',
        p_amount + p_fees,
        p_description || ' - Transfer Out',
        reference_number || '-OUT'
    );
    
    -- Process credit transaction (to account)
    to_transaction_id := process_transaction(
        p_to_account_id,
        'credit',
        p_amount,
        p_description || ' - Transfer In',
        reference_number || '-IN'
    );
    
    -- Create transfer record
    INSERT INTO transfers (
        from_account_id, to_account_id, from_transaction_id, to_transaction_id,
        transfer_type, fees
    ) VALUES (
        p_from_account_id, p_to_account_id, from_transaction_id, to_transaction_id,
        'internal', p_fees
    ) RETURNING id INTO transfer_id;
    
    RETURN transfer_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check for suspicious activity
CREATE OR REPLACE FUNCTION detect_suspicious_activity(
    p_user_id UUID,
    p_ip_address INET,
    p_action VARCHAR(100)
) RETURNS BOOLEAN AS $$
DECLARE
    recent_logins INTEGER;
    different_ips INTEGER;
    failed_attempts INTEGER;
    is_suspicious BOOLEAN := false;
BEGIN
    -- Check for multiple login attempts from different IPs in last hour
    SELECT COUNT(DISTINCT ip_address) INTO different_ips
    FROM audit_logs 
    WHERE user_id = p_user_id 
    AND action = 'login_attempt'
    AND created_at > NOW() - INTERVAL '1 hour';
    
    IF different_ips > 3 THEN
        is_suspicious := true;
        PERFORM log_security_event(
            p_user_id, 
            'multiple_ip_login', 
            'high',
            'Multiple IP addresses used for login attempts in short time',
            p_ip_address
        );
    END IF;
    
    -- Check for rapid successive transactions
    IF p_action LIKE '%transaction%' THEN
        SELECT COUNT(*) INTO recent_logins
        FROM audit_logs 
        WHERE user_id = p_user_id 
        AND action LIKE '%transaction%'
        AND created_at > NOW() - INTERVAL '5 minutes';
        
        IF recent_logins > 10 THEN
            is_suspicious := true;
            PERFORM log_security_event(
                p_user_id, 
                'rapid_transactions', 
                'medium',
                'Rapid successive transaction attempts detected',
                p_ip_address
            );
        END IF;
    END IF;
    
    -- Check for failed login attempts
    SELECT COUNT(*) INTO failed_attempts
    FROM audit_logs 
    WHERE user_id = p_user_id 
    AND action = 'login_failed'
    AND created_at > NOW() - INTERVAL '15 minutes';
    
    IF failed_attempts > 3 THEN
        is_suspicious := true;
        PERFORM log_security_event(
            p_user_id, 
            'multiple_failed_logins', 
            'high',
            'Multiple failed login attempts detected',
            p_ip_address
        );
    END IF;
    
    RETURN is_suspicious;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to generate account number
CREATE OR REPLACE FUNCTION generate_account_number(p_account_type account_type)
RETURNS VARCHAR(20) AS $$
DECLARE
    prefix VARCHAR(3);
    random_part VARCHAR(10);
    check_digit INTEGER;
    account_number VARCHAR(20);
BEGIN
    -- Set prefix based on account type
    CASE p_account_type
        WHEN 'checking' THEN prefix := '100';
        WHEN 'savings' THEN prefix := '200';
        WHEN 'investment' THEN prefix := '300';
        ELSE prefix := '999';
    END CASE;

    -- Generate random 10-digit number
    random_part := LPAD(FLOOR(RANDOM() * ***********)::TEXT, 10, '0');

    -- Calculate simple check digit (sum of digits mod 10)
    check_digit := (
        (prefix::INTEGER / 100) +
        ((prefix::INTEGER % 100) / 10) +
        (prefix::INTEGER % 10) +
        (SUBSTRING(random_part, 1, 1)::INTEGER) +
        (SUBSTRING(random_part, 2, 1)::INTEGER) +
        (SUBSTRING(random_part, 3, 1)::INTEGER) +
        (SUBSTRING(random_part, 4, 1)::INTEGER) +
        (SUBSTRING(random_part, 5, 1)::INTEGER) +
        (SUBSTRING(random_part, 6, 1)::INTEGER) +
        (SUBSTRING(random_part, 7, 1)::INTEGER) +
        (SUBSTRING(random_part, 8, 1)::INTEGER) +
        (SUBSTRING(random_part, 9, 1)::INTEGER) +
        (SUBSTRING(random_part, 10, 1)::INTEGER)
    ) % 10;

    account_number := prefix || random_part || check_digit::TEXT;

    -- Ensure uniqueness
    WHILE EXISTS (SELECT 1 FROM accounts WHERE account_number = account_number) LOOP
        random_part := LPAD(FLOOR(RANDOM() * ***********)::TEXT, 10, '0');
        account_number := prefix || random_part || check_digit::TEXT;
    END LOOP;

    RETURN account_number;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get security statistics for admin dashboard
CREATE OR REPLACE FUNCTION get_security_stats()
RETURNS JSONB AS $$
DECLARE
    result JSONB;
    total_users INTEGER;
    active_users INTEGER;
    locked_accounts INTEGER;
    failed_logins_24h INTEGER;
    security_events_24h INTEGER;
    high_severity_events INTEGER;
    unresolved_events INTEGER;
    suspicious_activities INTEGER;
BEGIN
    -- Get user statistics
    SELECT COUNT(*) INTO total_users FROM users;
    SELECT COUNT(*) INTO active_users FROM users WHERE is_active = true;
    SELECT COUNT(*) INTO locked_accounts FROM users WHERE locked_until > NOW();

    -- Get security event statistics
    SELECT COUNT(*) INTO failed_logins_24h
    FROM audit_logs
    WHERE action = 'login_failed'
    AND created_at > NOW() - INTERVAL '24 hours';

    SELECT COUNT(*) INTO security_events_24h
    FROM security_events
    WHERE created_at > NOW() - INTERVAL '24 hours';

    SELECT COUNT(*) INTO high_severity_events
    FROM security_events
    WHERE severity = 'high'
    AND created_at > NOW() - INTERVAL '7 days';

    SELECT COUNT(*) INTO unresolved_events
    FROM security_events
    WHERE resolved = false;

    SELECT COUNT(*) INTO suspicious_activities
    FROM security_events
    WHERE event_type LIKE '%suspicious%'
    AND created_at > NOW() - INTERVAL '24 hours';

    -- Build result JSON
    result := jsonb_build_object(
        'users', jsonb_build_object(
            'total', total_users,
            'active', active_users,
            'locked', locked_accounts
        ),
        'security', jsonb_build_object(
            'failed_logins_24h', failed_logins_24h,
            'security_events_24h', security_events_24h,
            'high_severity_events_7d', high_severity_events,
            'unresolved_events', unresolved_events,
            'suspicious_activities_24h', suspicious_activities
        ),
        'generated_at', NOW()
    );

    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
