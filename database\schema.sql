-- Secure Financial Portal Database Schema
-- This schema implements security best practices including:
-- 1. Row Level Security (RLS)
-- 2. Audit logging
-- 3. Data encryption for sensitive fields
-- 4. Proper indexing for performance
-- 5. Constraints for data integrity

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- <PERSON><PERSON> custom types
CREATE TYPE user_role AS ENUM ('user', 'admin', 'manager');
CREATE TYPE account_type AS ENUM ('checking', 'savings', 'investment');
CREATE TYPE transaction_type AS ENUM ('debit', 'credit', 'transfer');
CREATE TYPE transaction_status AS ENUM ('pending', 'completed', 'failed', 'cancelled');
CREATE TYPE currency_type AS ENUM ('USD', 'EUR', 'GBP');

-- Users table with security features
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    first_name <PERSON><PERSON><PERSON><PERSON>(50) NOT NULL,
    last_name <PERSON><PERSON><PERSON><PERSON>(50) NOT NULL,
    phone_number <PERSON><PERSON>HA<PERSON>(20),
    date_of_birth DATE NOT NULL,
    role user_role DEFAULT 'user',
    is_active BOOLEAN DEFAULT true,
    email_verified BOOLEAN DEFAULT false,
    phone_verified BOOLEAN DEFAULT false,
    two_factor_enabled BOOLEAN DEFAULT false,
    two_factor_secret TEXT,
    last_login TIMESTAMPTZ,
    failed_login_attempts INTEGER DEFAULT 0,
    locked_until TIMESTAMPTZ,
    password_changed_at TIMESTAMPTZ DEFAULT NOW(),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT users_email_check CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
    CONSTRAINT users_phone_check CHECK (phone_number ~* '^\+?[\d\s\-\(\)]+$'),
    CONSTRAINT users_age_check CHECK (date_of_birth <= CURRENT_DATE - INTERVAL '18 years'),
    CONSTRAINT users_failed_attempts_check CHECK (failed_login_attempts >= 0)
);

-- User profiles table for additional information
CREATE TABLE user_profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    address_street VARCHAR(100),
    address_city VARCHAR(50),
    address_state VARCHAR(50),
    address_zip VARCHAR(10),
    address_country VARCHAR(50),
    occupation VARCHAR(100),
    annual_income DECIMAL(15,2),
    profile_picture_url TEXT,
    preferences JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(user_id)
);

-- Accounts table
CREATE TABLE accounts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    account_number VARCHAR(20) UNIQUE NOT NULL,
    account_type account_type NOT NULL,
    balance DECIMAL(15,2) DEFAULT 0.00,
    currency currency_type DEFAULT 'USD',
    is_active BOOLEAN DEFAULT true,
    daily_limit DECIMAL(15,2) DEFAULT 10000.00,
    monthly_limit DECIMAL(15,2) DEFAULT 100000.00,
    overdraft_limit DECIMAL(15,2) DEFAULT 0.00,
    interest_rate DECIMAL(5,4) DEFAULT 0.0000,
    opened_date DATE DEFAULT CURRENT_DATE,
    closed_date DATE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT accounts_balance_check CHECK (balance >= -overdraft_limit),
    CONSTRAINT accounts_limits_check CHECK (daily_limit >= 0 AND monthly_limit >= 0),
    CONSTRAINT accounts_dates_check CHECK (closed_date IS NULL OR closed_date >= opened_date)
);

-- Transactions table
CREATE TABLE transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    account_id UUID REFERENCES accounts(id) ON DELETE CASCADE,
    transaction_type transaction_type NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    balance_after DECIMAL(15,2) NOT NULL,
    description TEXT NOT NULL,
    reference_number VARCHAR(50) UNIQUE NOT NULL,
    external_reference VARCHAR(100),
    status transaction_status DEFAULT 'pending',
    scheduled_date TIMESTAMPTZ,
    processed_date TIMESTAMPTZ,
    merchant_name VARCHAR(100),
    merchant_category VARCHAR(50),
    location JSONB,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT transactions_amount_check CHECK (amount > 0),
    CONSTRAINT transactions_dates_check CHECK (processed_date IS NULL OR processed_date >= created_at)
);

-- Transfer transactions (for internal transfers)
CREATE TABLE transfers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    from_account_id UUID REFERENCES accounts(id) ON DELETE CASCADE,
    to_account_id UUID REFERENCES accounts(id) ON DELETE CASCADE,
    from_transaction_id UUID REFERENCES transactions(id) ON DELETE CASCADE,
    to_transaction_id UUID REFERENCES transactions(id) ON DELETE CASCADE,
    transfer_type VARCHAR(20) DEFAULT 'internal',
    exchange_rate DECIMAL(10,6) DEFAULT 1.000000,
    fees DECIMAL(15,2) DEFAULT 0.00,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT transfers_different_accounts CHECK (from_account_id != to_account_id),
    CONSTRAINT transfers_exchange_rate_check CHECK (exchange_rate > 0)
);

-- Payees table for bill payments
CREATE TABLE payees (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    account_number VARCHAR(50),
    routing_number VARCHAR(20),
    address JSONB,
    category VARCHAR(50),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Bill payments table
CREATE TABLE bill_payments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    account_id UUID REFERENCES accounts(id) ON DELETE CASCADE,
    payee_id UUID REFERENCES payees(id) ON DELETE CASCADE,
    transaction_id UUID REFERENCES transactions(id) ON DELETE CASCADE,
    amount DECIMAL(15,2) NOT NULL,
    due_date DATE NOT NULL,
    memo TEXT,
    confirmation_number VARCHAR(50),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT bill_payments_amount_check CHECK (amount > 0)
);

-- Sessions table for session management
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    session_token TEXT UNIQUE NOT NULL,
    refresh_token TEXT UNIQUE NOT NULL,
    ip_address INET NOT NULL,
    user_agent TEXT,
    is_active BOOLEAN DEFAULT true,
    expires_at TIMESTAMPTZ NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    last_accessed TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT sessions_expiry_check CHECK (expires_at > created_at)
);

-- Audit logs table for security monitoring
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    action VARCHAR(100) NOT NULL,
    resource VARCHAR(100) NOT NULL,
    resource_id UUID,
    old_values JSONB,
    new_values JSONB,
    ip_address INET NOT NULL,
    user_agent TEXT,
    success BOOLEAN DEFAULT true,
    error_message TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Index for performance
    INDEX idx_audit_logs_user_id ON audit_logs(user_id),
    INDEX idx_audit_logs_action ON audit_logs(action),
    INDEX idx_audit_logs_created_at ON audit_logs(created_at),
    INDEX idx_audit_logs_resource ON audit_logs(resource, resource_id)
);

-- Security events table
CREATE TABLE security_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    event_type VARCHAR(50) NOT NULL,
    severity VARCHAR(20) DEFAULT 'medium',
    description TEXT NOT NULL,
    ip_address INET NOT NULL,
    user_agent TEXT,
    metadata JSONB DEFAULT '{}',
    resolved BOOLEAN DEFAULT false,
    resolved_at TIMESTAMPTZ,
    resolved_by UUID REFERENCES users(id) ON DELETE SET NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Index for performance
    INDEX idx_security_events_user_id ON security_events(user_id),
    INDEX idx_security_events_type ON security_events(event_type),
    INDEX idx_security_events_severity ON security_events(severity),
    INDEX idx_security_events_created_at ON security_events(created_at)
);

-- API keys table for external integrations
CREATE TABLE api_keys (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    key_hash TEXT UNIQUE NOT NULL,
    permissions JSONB DEFAULT '[]',
    is_active BOOLEAN DEFAULT true,
    last_used TIMESTAMPTZ,
    expires_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT api_keys_expiry_check CHECK (expires_at IS NULL OR expires_at > created_at)
);

-- Create indexes for performance
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_active ON users(is_active);
CREATE INDEX idx_accounts_user_id ON accounts(user_id);
CREATE INDEX idx_accounts_number ON accounts(account_number);
CREATE INDEX idx_accounts_active ON accounts(is_active);
CREATE INDEX idx_transactions_account_id ON transactions(account_id);
CREATE INDEX idx_transactions_type ON transactions(transaction_type);
CREATE INDEX idx_transactions_status ON transactions(status);
CREATE INDEX idx_transactions_created_at ON transactions(created_at);
CREATE INDEX idx_transactions_reference ON transactions(reference_number);
CREATE INDEX idx_transfers_from_account ON transfers(from_account_id);
CREATE INDEX idx_transfers_to_account ON transfers(to_account_id);
CREATE INDEX idx_sessions_user_id ON user_sessions(user_id);
CREATE INDEX idx_sessions_token ON user_sessions(session_token);
CREATE INDEX idx_sessions_active ON user_sessions(is_active);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at triggers
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE ON user_profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_accounts_updated_at BEFORE UPDATE ON accounts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_transactions_updated_at BEFORE UPDATE ON transactions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_payees_updated_at BEFORE UPDATE ON payees FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security (RLS)
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE transfers ENABLE ROW LEVEL SECURITY;
ALTER TABLE payees ENABLE ROW LEVEL SECURITY;
ALTER TABLE bill_payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE security_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE api_keys ENABLE ROW LEVEL SECURITY;

-- Helper function to get current user ID from JWT
CREATE OR REPLACE FUNCTION auth.user_id() RETURNS UUID AS $$
  SELECT COALESCE(
    auth.jwt() ->> 'sub',
    (current_setting('request.jwt.claim.sub', true))
  )::UUID;
$$ LANGUAGE SQL STABLE;

-- Helper function to check if user is admin
CREATE OR REPLACE FUNCTION auth.is_admin() RETURNS BOOLEAN AS $$
  SELECT EXISTS (
    SELECT 1 FROM users
    WHERE id = auth.user_id()
    AND role IN ('admin', 'manager')
    AND is_active = true
  );
$$ LANGUAGE SQL STABLE;

-- RLS Policies for users table
CREATE POLICY "Users can view their own profile" ON users
  FOR SELECT USING (id = auth.user_id());

CREATE POLICY "Users can update their own profile" ON users
  FOR UPDATE USING (id = auth.user_id());

CREATE POLICY "Admins can view all users" ON users
  FOR SELECT USING (auth.is_admin());

CREATE POLICY "Admins can update all users" ON users
  FOR UPDATE USING (auth.is_admin());

-- RLS Policies for user_profiles table
CREATE POLICY "Users can view their own profile details" ON user_profiles
  FOR SELECT USING (user_id = auth.user_id());

CREATE POLICY "Users can update their own profile details" ON user_profiles
  FOR UPDATE USING (user_id = auth.user_id());

CREATE POLICY "Users can insert their own profile details" ON user_profiles
  FOR INSERT WITH CHECK (user_id = auth.user_id());

CREATE POLICY "Admins can view all profiles" ON user_profiles
  FOR SELECT USING (auth.is_admin());

-- RLS Policies for accounts table
CREATE POLICY "Users can view their own accounts" ON accounts
  FOR SELECT USING (user_id = auth.user_id());

CREATE POLICY "Users can update their own accounts" ON accounts
  FOR UPDATE USING (user_id = auth.user_id());

CREATE POLICY "Users can insert their own accounts" ON accounts
  FOR INSERT WITH CHECK (user_id = auth.user_id());

CREATE POLICY "Admins can view all accounts" ON accounts
  FOR SELECT USING (auth.is_admin());

CREATE POLICY "Admins can update all accounts" ON accounts
  FOR UPDATE USING (auth.is_admin());

-- RLS Policies for transactions table
CREATE POLICY "Users can view their own transactions" ON transactions
  FOR SELECT USING (
    account_id IN (
      SELECT id FROM accounts WHERE user_id = auth.user_id()
    )
  );

CREATE POLICY "Users can insert transactions for their accounts" ON transactions
  FOR INSERT WITH CHECK (
    account_id IN (
      SELECT id FROM accounts WHERE user_id = auth.user_id()
    )
  );

CREATE POLICY "Admins can view all transactions" ON transactions
  FOR SELECT USING (auth.is_admin());

CREATE POLICY "System can update transactions" ON transactions
  FOR UPDATE USING (true);

-- RLS Policies for transfers table
CREATE POLICY "Users can view their own transfers" ON transfers
  FOR SELECT USING (
    from_account_id IN (
      SELECT id FROM accounts WHERE user_id = auth.user_id()
    ) OR to_account_id IN (
      SELECT id FROM accounts WHERE user_id = auth.user_id()
    )
  );

CREATE POLICY "Users can insert transfers from their accounts" ON transfers
  FOR INSERT WITH CHECK (
    from_account_id IN (
      SELECT id FROM accounts WHERE user_id = auth.user_id()
    )
  );

CREATE POLICY "Admins can view all transfers" ON transfers
  FOR SELECT USING (auth.is_admin());

-- RLS Policies for payees table
CREATE POLICY "Users can manage their own payees" ON payees
  FOR ALL USING (user_id = auth.user_id());

CREATE POLICY "Admins can view all payees" ON payees
  FOR SELECT USING (auth.is_admin());

-- RLS Policies for bill_payments table
CREATE POLICY "Users can view their own bill payments" ON bill_payments
  FOR SELECT USING (
    account_id IN (
      SELECT id FROM accounts WHERE user_id = auth.user_id()
    )
  );

CREATE POLICY "Users can insert bill payments for their accounts" ON bill_payments
  FOR INSERT WITH CHECK (
    account_id IN (
      SELECT id FROM accounts WHERE user_id = auth.user_id()
    )
  );

CREATE POLICY "Admins can view all bill payments" ON bill_payments
  FOR SELECT USING (auth.is_admin());

-- RLS Policies for user_sessions table
CREATE POLICY "Users can view their own sessions" ON user_sessions
  FOR SELECT USING (user_id = auth.user_id());

CREATE POLICY "Users can update their own sessions" ON user_sessions
  FOR UPDATE USING (user_id = auth.user_id());

CREATE POLICY "Users can insert their own sessions" ON user_sessions
  FOR INSERT WITH CHECK (user_id = auth.user_id());

CREATE POLICY "Admins can view all sessions" ON user_sessions
  FOR SELECT USING (auth.is_admin());

-- RLS Policies for audit_logs table
CREATE POLICY "Users can view their own audit logs" ON audit_logs
  FOR SELECT USING (user_id = auth.user_id());

CREATE POLICY "Admins can view all audit logs" ON audit_logs
  FOR SELECT USING (auth.is_admin());

CREATE POLICY "System can insert audit logs" ON audit_logs
  FOR INSERT WITH CHECK (true);

-- RLS Policies for security_events table
CREATE POLICY "Admins can manage security events" ON security_events
  FOR ALL USING (auth.is_admin());

CREATE POLICY "System can insert security events" ON security_events
  FOR INSERT WITH CHECK (true);

-- RLS Policies for api_keys table
CREATE POLICY "Users can manage their own API keys" ON api_keys
  FOR ALL USING (user_id = auth.user_id());

CREATE POLICY "Admins can view all API keys" ON api_keys
  FOR SELECT USING (auth.is_admin());
