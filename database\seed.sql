-- Seed data for testing the secure financial portal
-- This includes test users, accounts, and sample transactions

-- Insert test users (passwords are hashed versions of 'SecurePass123!')
INSERT INTO users (id, email, password_hash, first_name, last_name, phone_number, date_of_birth, role, email_verified) VALUES
(
    '550e8400-e29b-41d4-a716-************',
    '<EMAIL>',
    '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qK', -- SecurePass123!
    'Admin',
    'User',
    '******-0001',
    '1985-01-15',
    'admin',
    true
),
(
    '550e8400-e29b-41d4-a716-************',
    '<EMAIL>',
    '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qK', -- SecurePass123!
    'Manager',
    'User',
    '******-0002',
    '1988-03-22',
    'manager',
    true
),
(
    '550e8400-e29b-41d4-a716-************',
    '<EMAIL>',
    '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qK', -- SecurePass123!
    'John',
    'Doe',
    '******-0003',
    '1990-05-10',
    'user',
    true
),
(
    '550e8400-e29b-41d4-a716-************',
    '<EMAIL>',
    '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qK', -- SecurePass123!
    'Jane',
    'Smith',
    '******-0004',
    '1992-08-18',
    'user',
    true
),
(
    '550e8400-e29b-41d4-a716-************',
    '<EMAIL>',
    '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qK', -- SecurePass123!
    'Bob',
    'Wilson',
    '******-0005',
    '1987-12-03',
    'user',
    true
);

-- Insert user profiles
INSERT INTO user_profiles (user_id, address_street, address_city, address_state, address_zip, address_country, occupation, annual_income) VALUES
(
    '550e8400-e29b-41d4-a716-************',
    '123 Admin St',
    'New York',
    'NY',
    '10001',
    'USA',
    'System Administrator',
    120000.00
),
(
    '550e8400-e29b-41d4-a716-************',
    '456 Manager Ave',
    'Los Angeles',
    'CA',
    '90001',
    'USA',
    'Bank Manager',
    95000.00
),
(
    '550e8400-e29b-41d4-a716-************',
    '789 Main St',
    'Chicago',
    'IL',
    '60601',
    'USA',
    'Software Engineer',
    85000.00
),
(
    '550e8400-e29b-41d4-a716-************',
    '321 Oak Ave',
    'Houston',
    'TX',
    '77001',
    'USA',
    'Marketing Manager',
    75000.00
),
(
    '550e8400-e29b-41d4-a716-************',
    '654 Pine St',
    'Phoenix',
    'AZ',
    '85001',
    'USA',
    'Sales Representative',
    65000.00
);

-- Insert test accounts
INSERT INTO accounts (id, user_id, account_number, account_type, balance, currency, daily_limit, monthly_limit) VALUES
(
    '660e8400-e29b-41d4-a716-************',
    '550e8400-e29b-41d4-a716-************',
    '100**********1',
    'checking',
    5000.00,
    'USD',
    2000.00,
    50000.00
),
(
    '660e8400-e29b-41d4-a716-************',
    '550e8400-e29b-41d4-a716-************',
    '200**********2',
    'savings',
    15000.00,
    'USD',
    1000.00,
    25000.00
),
(
    '660e8400-e29b-41d4-a716-************',
    '550e8400-e29b-41d4-a716-************',
    '100**********3',
    'checking',
    3500.00,
    'USD',
    1500.00,
    40000.00
),
(
    '660e8400-e29b-41d4-a716-************',
    '550e8400-e29b-41d4-a716-************',
    '200**********4',
    'savings',
    8000.00,
    'USD',
    800.00,
    20000.00
),
(
    '660e8400-e29b-41d4-a716-************',
    '550e8400-e29b-41d4-a716-************',
    '100**********5',
    'checking',
    2200.00,
    'USD',
    1200.00,
    30000.00
),
(
    '660e8400-e29b-41d4-a716-************',
    '550e8400-e29b-41d4-a716-************',
    '300**********6',
    'investment',
    25000.00,
    'USD',
    5000.00,
    100000.00
);

-- Insert sample transactions
INSERT INTO transactions (account_id, transaction_type, amount, balance_after, description, reference_number, status, processed_date) VALUES
(
    '660e8400-e29b-41d4-a716-************',
    'credit',
    1000.00,
    5000.00,
    'Direct Deposit - Salary',
    'TXN' || TO_CHAR(NOW() - INTERVAL '5 days', 'YYYYMMDD') || '001',
    'completed',
    NOW() - INTERVAL '5 days'
),
(
    '660e8400-e29b-41d4-a716-************',
    'debit',
    50.00,
    4950.00,
    'ATM Withdrawal',
    'TXN' || TO_CHAR(NOW() - INTERVAL '4 days', 'YYYYMMDD') || '002',
    'completed',
    NOW() - INTERVAL '4 days'
),
(
    '660e8400-e29b-41d4-a716-************',
    'debit',
    25.99,
    4924.01,
    'Online Purchase - Amazon',
    'TXN' || TO_CHAR(NOW() - INTERVAL '3 days', 'YYYYMMDD') || '003',
    'completed',
    NOW() - INTERVAL '3 days'
),
(
    '660e8400-e29b-41d4-a716-************',
    'credit',
    500.00,
    15000.00,
    'Transfer from Checking',
    'TXN' || TO_CHAR(NOW() - INTERVAL '2 days', 'YYYYMMDD') || '004',
    'completed',
    NOW() - INTERVAL '2 days'
),
(
    '660e8400-e29b-41d4-a716-************',
    'credit',
    2000.00,
    3500.00,
    'Direct Deposit - Salary',
    'TXN' || TO_CHAR(NOW() - INTERVAL '7 days', 'YYYYMMDD') || '005',
    'completed',
    NOW() - INTERVAL '7 days'
),
(
    '660e8400-e29b-41d4-a716-************',
    'debit',
    75.00,
    3425.00,
    'Grocery Store Purchase',
    'TXN' || TO_CHAR(NOW() - INTERVAL '1 day', 'YYYYMMDD') || '006',
    'completed',
    NOW() - INTERVAL '1 day'
);

-- Insert sample payees
INSERT INTO payees (user_id, name, account_number, routing_number, category) VALUES
(
    '550e8400-e29b-41d4-a716-************',
    'Electric Company',
    '**********',
    '*********',
    'Utilities'
),
(
    '550e8400-e29b-41d4-a716-************',
    'Internet Provider',
    '**********',
    '*********',
    'Utilities'
),
(
    '550e8400-e29b-41d4-a716-************',
    'Credit Card Company',
    '**********',
    '*********',
    'Credit Cards'
),
(
    '550e8400-e29b-41d4-a716-************',
    'Mortgage Company',
    '**********',
    '*********',
    'Loans'
);

-- Insert sample audit logs
INSERT INTO audit_logs (user_id, action, resource, resource_id, ip_address, user_agent, success) VALUES
(
    '550e8400-e29b-41d4-a716-************',
    'login_success',
    'user_session',
    '550e8400-e29b-41d4-a716-************',
    '*************',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    true
),
(
    '550e8400-e29b-41d4-a716-************',
    'account_view',
    'account',
    '660e8400-e29b-41d4-a716-************',
    '*************',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
    true
),
(
    '550e8400-e29b-41d4-a716-************',
    'transaction_create',
    'transaction',
    '660e8400-e29b-41d4-a716-************',
    '*************',
    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36',
    true
);

-- Insert sample security events
INSERT INTO security_events (user_id, event_type, severity, description, ip_address, user_agent) VALUES
(
    '550e8400-e29b-41d4-a716-************',
    'login_from_new_device',
    'medium',
    'User logged in from a new device/location',
    '***********',
    'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15'
),
(
    NULL,
    'brute_force_attempt',
    'high',
    'Multiple failed login attempts detected from IP',
    '************',
    'curl/7.68.0'
);

-- Update sequences to avoid conflicts
SELECT setval('users_id_seq', (SELECT MAX(id::text)::bigint FROM users WHERE id::text ~ '^\d+$'), true);
SELECT setval('accounts_id_seq', (SELECT MAX(id::text)::bigint FROM accounts WHERE id::text ~ '^\d+$'), true);
SELECT setval('transactions_id_seq', (SELECT MAX(id::text)::bigint FROM transactions WHERE id::text ~ '^\d+$'), true);
