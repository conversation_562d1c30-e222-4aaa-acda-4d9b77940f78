import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@/lib/supabase';
import { getClientIP, getUserAgent, verifyAccessToken } from '@/utils/security';
import { createAccountSchema, validateInput } from '@/utils/validation';

// GET /api/accounts - Get user's accounts
export async function GET(request: NextRequest) {
  try {
    // Get client information
    const clientIP = getClientIP(request);
    const userAgent = getUserAgent(request);
    
    // Create Supabase client
    const supabase = createRouteHandlerClient(request, NextResponse.next());
    
    // Get current session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user's accounts
    const { data: accounts, error: accountsError } = await supabase
      .from('accounts')
      .select(`
        id,
        account_number,
        account_type,
        balance,
        currency,
        is_active,
        daily_limit,
        monthly_limit,
        opened_date,
        created_at,
        updated_at
      `)
      .eq('user_id', session.user.id)
      .eq('is_active', true)
      .order('created_at', { ascending: false });

    if (accountsError) {
      console.error('Accounts fetch error:', accountsError);
      
      await supabase.rpc('log_audit_event', {
        p_user_id: session.user.id,
        p_action: 'accounts_fetch_failed',
        p_resource: 'account',
        p_ip_address: clientIP,
        p_user_agent: userAgent,
        p_success: false,
        p_error_message: accountsError.message
      });

      return NextResponse.json(
        { success: false, error: 'Failed to fetch accounts' },
        { status: 500 }
      );
    }

    // Calculate daily and monthly usage for each account
    const accountsWithUsage = await Promise.all(
      accounts.map(async (account) => {
        // Get daily usage
        const { data: dailyTransactions } = await supabase
          .from('transactions')
          .select('amount')
          .eq('account_id', account.id)
          .eq('transaction_type', 'debit')
          .eq('status', 'completed')
          .gte('created_at', new Date().toISOString().split('T')[0] + 'T00:00:00.000Z');

        const dailyUsage = dailyTransactions?.reduce((sum, tx) => sum + tx.amount, 0) || 0;

        // Get monthly usage
        const monthStart = new Date();
        monthStart.setDate(1);
        monthStart.setHours(0, 0, 0, 0);

        const { data: monthlyTransactions } = await supabase
          .from('transactions')
          .select('amount')
          .eq('account_id', account.id)
          .eq('transaction_type', 'debit')
          .eq('status', 'completed')
          .gte('created_at', monthStart.toISOString());

        const monthlyUsage = monthlyTransactions?.reduce((sum, tx) => sum + tx.amount, 0) || 0;

        return {
          ...account,
          dailyUsage,
          monthlyUsage,
          dailyRemaining: Math.max(0, account.daily_limit - dailyUsage),
          monthlyRemaining: Math.max(0, account.monthly_limit - monthlyUsage)
        };
      })
    );

    // Log successful access
    await supabase.rpc('log_audit_event', {
      p_user_id: session.user.id,
      p_action: 'accounts_viewed',
      p_resource: 'account',
      p_ip_address: clientIP,
      p_user_agent: userAgent,
      p_success: true,
      p_metadata: { accounts_count: accounts.length }
    });

    return NextResponse.json({
      success: true,
      data: accountsWithUsage,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Accounts API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/accounts - Create new account
export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const body = await request.json();
    
    // Validate input
    const validation = validateInput(createAccountSchema, body);
    if (!validation.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid input',
          details: validation.errors 
        },
        { status: 400 }
      );
    }

    const { accountType, initialDeposit, currency } = validation.data;
    
    // Get client information
    const clientIP = getClientIP(request);
    const userAgent = getUserAgent(request);
    
    // Create Supabase client
    const supabase = createRouteHandlerClient(request, NextResponse.next());
    
    // Get current session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user already has this type of account
    const { data: existingAccount } = await supabase
      .from('accounts')
      .select('id')
      .eq('user_id', session.user.id)
      .eq('account_type', accountType)
      .eq('is_active', true)
      .single();

    if (existingAccount) {
      return NextResponse.json(
        { success: false, error: `You already have an active ${accountType} account` },
        { status: 409 }
      );
    }

    // Generate account number using database function
    const { data: accountNumber } = await supabase.rpc('generate_account_number', {
      p_account_type: accountType
    });

    if (!accountNumber) {
      return NextResponse.json(
        { success: false, error: 'Failed to generate account number' },
        { status: 500 }
      );
    }

    // Set account limits based on type
    let dailyLimit = 1000;
    let monthlyLimit = 25000;
    
    switch (accountType) {
      case 'checking':
        dailyLimit = 2000;
        monthlyLimit = 50000;
        break;
      case 'savings':
        dailyLimit = 1000;
        monthlyLimit = 25000;
        break;
      case 'investment':
        dailyLimit = 5000;
        monthlyLimit = 100000;
        break;
    }

    // Create account
    const { data: newAccount, error: accountError } = await supabase
      .from('accounts')
      .insert({
        user_id: session.user.id,
        account_number: accountNumber,
        account_type: accountType,
        balance: initialDeposit,
        currency: currency,
        daily_limit: dailyLimit,
        monthly_limit: monthlyLimit,
        is_active: true
      })
      .select()
      .single();

    if (accountError) {
      console.error('Account creation error:', accountError);
      
      await supabase.rpc('log_audit_event', {
        p_user_id: session.user.id,
        p_action: 'account_creation_failed',
        p_resource: 'account',
        p_ip_address: clientIP,
        p_user_agent: userAgent,
        p_success: false,
        p_error_message: accountError.message
      });

      return NextResponse.json(
        { success: false, error: 'Failed to create account' },
        { status: 500 }
      );
    }

    // Create initial deposit transaction if amount > 0
    if (initialDeposit > 0) {
      const { error: transactionError } = await supabase.rpc('process_transaction', {
        p_account_id: newAccount.id,
        p_transaction_type: 'credit',
        p_amount: initialDeposit,
        p_description: 'Initial deposit',
        p_reference_number: `INIT${Date.now()}`,
        p_metadata: { account_opening: true }
      });

      if (transactionError) {
        console.error('Initial deposit transaction error:', transactionError);
      }
    }

    // Log successful account creation
    await supabase.rpc('log_audit_event', {
      p_user_id: session.user.id,
      p_action: 'account_created',
      p_resource: 'account',
      p_resource_id: newAccount.id,
      p_ip_address: clientIP,
      p_user_agent: userAgent,
      p_success: true,
      p_metadata: {
        account_type: accountType,
        initial_deposit: initialDeposit,
        currency: currency
      }
    });

    return NextResponse.json({
      success: true,
      message: 'Account created successfully',
      data: {
        id: newAccount.id,
        accountNumber: newAccount.account_number,
        accountType: newAccount.account_type,
        balance: newAccount.balance,
        currency: newAccount.currency,
        dailyLimit: newAccount.daily_limit,
        monthlyLimit: newAccount.monthly_limit
      },
      timestamp: new Date().toISOString()
    }, { status: 201 });

  } catch (error) {
    console.error('Account creation API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
