import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@/lib/supabase';
import { getClientIP, getUserAgent } from '@/utils/security';

// GET /api/admin/security - Get security events and audit logs (Admin only)
export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = Math.min(parseInt(searchParams.get('limit') || '50'), 100);
    const eventType = searchParams.get('eventType');
    const severity = searchParams.get('severity');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const userId = searchParams.get('userId');
    
    // Get client information
    const clientIP = getClientIP(request);
    const userAgent = getUserAgent(request);
    
    // Create Supabase client
    const supabase = createRouteHandlerClient(request, NextResponse.next());
    
    // Get current session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user is admin
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('role')
      .eq('id', session.user.id)
      .single();

    if (userError || !user || (user.role !== 'admin' && user.role !== 'manager')) {
      await supabase.rpc('log_security_event', {
        p_user_id: session.user.id,
        p_event_type: 'unauthorized_admin_access',
        p_severity: 'high',
        p_description: 'Unauthorized attempt to access admin security endpoint',
        p_ip_address: clientIP,
        p_user_agent: userAgent
      });

      return NextResponse.json(
        { success: false, error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    // Build security events query
    let securityQuery = supabase
      .from('security_events')
      .select(`
        id,
        user_id,
        event_type,
        severity,
        description,
        ip_address,
        user_agent,
        metadata,
        resolved,
        resolved_at,
        resolved_by,
        created_at,
        users(
          email,
          first_name,
          last_name
        )
      `, { count: 'exact' });

    // Apply filters
    if (eventType) {
      securityQuery = securityQuery.eq('event_type', eventType);
    }
    
    if (severity) {
      securityQuery = securityQuery.eq('severity', severity);
    }
    
    if (startDate) {
      securityQuery = securityQuery.gte('created_at', startDate);
    }
    
    if (endDate) {
      securityQuery = securityQuery.lte('created_at', endDate);
    }
    
    if (userId) {
      securityQuery = securityQuery.eq('user_id', userId);
    }

    // Apply pagination and ordering
    const offset = (page - 1) * limit;
    securityQuery = securityQuery
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    const { data: securityEvents, error: securityError, count: securityCount } = await securityQuery;

    if (securityError) {
      console.error('Security events fetch error:', securityError);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch security events' },
        { status: 500 }
      );
    }

    // Get recent audit logs
    const { data: auditLogs, error: auditError } = await supabase
      .from('audit_logs')
      .select(`
        id,
        user_id,
        action,
        resource,
        resource_id,
        ip_address,
        user_agent,
        success,
        error_message,
        created_at,
        users(
          email,
          first_name,
          last_name
        )
      `)
      .order('created_at', { ascending: false })
      .limit(20);

    if (auditError) {
      console.error('Audit logs fetch error:', auditError);
    }

    // Get security statistics
    const { data: stats } = await supabase.rpc('get_security_stats');

    // Log admin access
    await supabase.rpc('log_audit_event', {
      p_user_id: session.user.id,
      p_action: 'admin_security_dashboard_viewed',
      p_resource: 'admin_security',
      p_ip_address: clientIP,
      p_user_agent: userAgent,
      p_success: true,
      p_metadata: {
        filters: {
          eventType,
          severity,
          startDate,
          endDate,
          userId
        }
      }
    });

    // Calculate pagination
    const totalPages = Math.ceil((securityCount || 0) / limit);

    return NextResponse.json({
      success: true,
      data: {
        securityEvents: securityEvents || [],
        auditLogs: auditLogs || [],
        statistics: stats || {}
      },
      pagination: {
        page,
        limit,
        total: securityCount || 0,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Admin security API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/admin/security - Resolve security event (Admin only)
export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const body = await request.json();
    const { eventId, resolution } = body;
    
    if (!eventId) {
      return NextResponse.json(
        { success: false, error: 'Event ID is required' },
        { status: 400 }
      );
    }
    
    // Get client information
    const clientIP = getClientIP(request);
    const userAgent = getUserAgent(request);
    
    // Create Supabase client
    const supabase = createRouteHandlerClient(request, NextResponse.next());
    
    // Get current session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user is admin
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('role')
      .eq('id', session.user.id)
      .single();

    if (userError || !user || (user.role !== 'admin' && user.role !== 'manager')) {
      return NextResponse.json(
        { success: false, error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get the security event
    const { data: securityEvent, error: eventError } = await supabase
      .from('security_events')
      .select('*')
      .eq('id', eventId)
      .single();

    if (eventError || !securityEvent) {
      return NextResponse.json(
        { success: false, error: 'Security event not found' },
        { status: 404 }
      );
    }

    // Update security event as resolved
    const { data: updatedEvent, error: updateError } = await supabase
      .from('security_events')
      .update({
        resolved: true,
        resolved_at: new Date().toISOString(),
        resolved_by: session.user.id,
        metadata: {
          ...securityEvent.metadata,
          resolution: resolution || 'Resolved by admin'
        }
      })
      .eq('id', eventId)
      .select()
      .single();

    if (updateError) {
      console.error('Security event update error:', updateError);
      return NextResponse.json(
        { success: false, error: 'Failed to resolve security event' },
        { status: 500 }
      );
    }

    // Log the resolution
    await supabase.rpc('log_audit_event', {
      p_user_id: session.user.id,
      p_action: 'security_event_resolved',
      p_resource: 'security_event',
      p_resource_id: eventId,
      p_ip_address: clientIP,
      p_user_agent: userAgent,
      p_success: true,
      p_metadata: {
        event_type: securityEvent.event_type,
        severity: securityEvent.severity,
        resolution: resolution
      }
    });

    return NextResponse.json({
      success: true,
      message: 'Security event resolved successfully',
      data: {
        id: updatedEvent.id,
        eventType: updatedEvent.event_type,
        severity: updatedEvent.severity,
        resolved: updatedEvent.resolved,
        resolvedAt: updatedEvent.resolved_at,
        resolvedBy: updatedEvent.resolved_by
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Security event resolution error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
