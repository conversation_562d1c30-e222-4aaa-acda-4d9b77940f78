import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@/lib/supabase';
import { 
  verifyPassword, 
  generateAccessToken, 
  generateRefreshToken,
  getClientIP,
  getUserAgent,
  isAccountLocked,
  calculateLockoutTime
} from '@/utils/security';
import { loginSchema } from '@/utils/validation';
import { z } from 'zod';

const MAX_LOGIN_ATTEMPTS = parseInt(process.env.MAX_LOGIN_ATTEMPTS || '5');
const LOCKOUT_TIME = parseInt(process.env.LOCKOUT_TIME || '900000'); // 15 minutes

export async function POST(request: NextRequest) {
  const response = NextResponse.next();
  
  try {
    // Parse request body
    const body = await request.json();
    
    // Validate input
    const validationResult = loginSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid input',
          details: validationResult.error.errors 
        },
        { status: 400 }
      );
    }

    const { email, password, rememberMe } = validationResult.data;
    
    // Get client information
    const clientIP = getClientIP(request);
    const userAgent = getUserAgent(request);
    
    // Create Supabase client
    const supabase = createRouteHandlerClient(request, response);
    
    // Get user from database
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('email', email)
      .single();

    if (userError || !user) {
      // Log failed login attempt
      await supabase.rpc('log_audit_event', {
        p_user_id: null,
        p_action: 'login_failed',
        p_resource: 'authentication',
        p_ip_address: clientIP,
        p_user_agent: userAgent,
        p_success: false,
        p_error_message: 'User not found'
      });

      return NextResponse.json(
        { success: false, error: 'Invalid credentials' },
        { status: 401 }
      );
    }

    // Check if account is locked
    if (isAccountLocked(user.locked_until)) {
      await supabase.rpc('log_security_event', {
        p_user_id: user.id,
        p_event_type: 'login_attempt_locked_account',
        p_severity: 'high',
        p_description: 'Login attempt on locked account',
        p_ip_address: clientIP,
        p_user_agent: userAgent
      });

      return NextResponse.json(
        { 
          success: false, 
          error: 'Account is temporarily locked. Please try again later.',
          lockedUntil: user.locked_until
        },
        { status: 423 }
      );
    }

    // Check if account is active
    if (!user.is_active) {
      await supabase.rpc('log_security_event', {
        p_user_id: user.id,
        p_event_type: 'login_attempt_inactive_account',
        p_severity: 'medium',
        p_description: 'Login attempt on inactive account',
        p_ip_address: clientIP,
        p_user_agent: userAgent
      });

      return NextResponse.json(
        { success: false, error: 'Account is deactivated' },
        { status: 403 }
      );
    }

    // Verify password
    const isPasswordValid = await verifyPassword(password, user.password_hash);
    
    if (!isPasswordValid) {
      // Increment failed login attempts
      const newFailedAttempts = user.failed_login_attempts + 1;
      let updateData: any = {
        failed_login_attempts: newFailedAttempts
      };

      // Lock account if max attempts reached
      if (newFailedAttempts >= MAX_LOGIN_ATTEMPTS) {
        updateData.locked_until = calculateLockoutTime(newFailedAttempts).toISOString();
        
        await supabase.rpc('log_security_event', {
          p_user_id: user.id,
          p_event_type: 'account_locked',
          p_severity: 'high',
          p_description: `Account locked after ${newFailedAttempts} failed login attempts`,
          p_ip_address: clientIP,
          p_user_agent: userAgent
        });
      }

      // Update user record
      await supabase
        .from('users')
        .update(updateData)
        .eq('id', user.id);

      // Log failed login attempt
      await supabase.rpc('log_audit_event', {
        p_user_id: user.id,
        p_action: 'login_failed',
        p_resource: 'authentication',
        p_ip_address: clientIP,
        p_user_agent: userAgent,
        p_success: false,
        p_error_message: 'Invalid password'
      });

      return NextResponse.json(
        { success: false, error: 'Invalid credentials' },
        { status: 401 }
      );
    }

    // Check for suspicious activity
    const { data: isSuspicious } = await supabase.rpc('detect_suspicious_activity', {
      p_user_id: user.id,
      p_ip_address: clientIP,
      p_action: 'login_attempt'
    });

    if (isSuspicious) {
      // Additional security measures could be implemented here
      // For now, we'll just log it and continue
    }

    // Generate tokens
    const tokenPayload = {
      userId: user.id,
      email: user.email,
      role: user.role
    };

    const accessToken = generateAccessToken(tokenPayload);
    const refreshToken = generateRefreshToken(tokenPayload);

    // Create session
    const sessionExpiry = rememberMe 
      ? new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days
      : new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

    const { data: session, error: sessionError } = await supabase
      .from('user_sessions')
      .insert({
        user_id: user.id,
        session_token: accessToken,
        refresh_token: refreshToken,
        ip_address: clientIP,
        user_agent: userAgent,
        expires_at: sessionExpiry.toISOString()
      })
      .select()
      .single();

    if (sessionError) {
      console.error('Session creation error:', sessionError);
      return NextResponse.json(
        { success: false, error: 'Failed to create session' },
        { status: 500 }
      );
    }

    // Reset failed login attempts and update last login
    await supabase
      .from('users')
      .update({
        failed_login_attempts: 0,
        locked_until: null,
        last_login: new Date().toISOString()
      })
      .eq('id', user.id);

    // Log successful login
    await supabase.rpc('log_audit_event', {
      p_user_id: user.id,
      p_action: 'login_success',
      p_resource: 'authentication',
      p_resource_id: session.id,
      p_ip_address: clientIP,
      p_user_agent: userAgent,
      p_success: true
    });

    // Set secure cookies
    const cookieOptions = {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict' as const,
      maxAge: rememberMe ? 30 * 24 * 60 * 60 : 24 * 60 * 60, // seconds
      path: '/'
    };

    response.cookies.set('access_token', accessToken, cookieOptions);
    response.cookies.set('refresh_token', refreshToken, {
      ...cookieOptions,
      maxAge: 7 * 24 * 60 * 60 // 7 days for refresh token
    });

    // Return success response
    return NextResponse.json({
      success: true,
      message: 'Login successful',
      user: {
        id: user.id,
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        role: user.role,
        emailVerified: user.email_verified,
        twoFactorEnabled: user.two_factor_enabled
      },
      tokens: {
        accessToken,
        refreshToken,
        expiresAt: sessionExpiry.toISOString()
      }
    });

  } catch (error) {
    console.error('Login error:', error);
    
    // Log error
    const clientIP = getClientIP(request);
    const userAgent = getUserAgent(request);
    
    try {
      const supabase = createRouteHandlerClient(request, response);
      await supabase.rpc('log_audit_event', {
        p_user_id: null,
        p_action: 'login_error',
        p_resource: 'authentication',
        p_ip_address: clientIP,
        p_user_agent: userAgent,
        p_success: false,
        p_error_message: error instanceof Error ? error.message : 'Unknown error'
      });
    } catch (logError) {
      console.error('Failed to log error:', logError);
    }

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
