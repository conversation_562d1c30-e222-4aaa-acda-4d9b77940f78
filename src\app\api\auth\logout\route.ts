import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@/lib/supabase';
import { getClientIP, getUserAgent } from '@/utils/security';

export async function POST(request: NextRequest) {
  const response = NextResponse.next();
  
  try {
    // Get client information
    const clientIP = getClientIP(request);
    const userAgent = getUserAgent(request);
    
    // Create Supabase client
    const supabase = createRouteHandlerClient(request, response);
    
    // Get current session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      return NextResponse.json(
        { success: false, error: 'No active session found' },
        { status: 401 }
      );
    }

    // Get access token from cookies or headers
    const accessToken = request.cookies.get('access_token')?.value || 
                       request.headers.get('authorization')?.replace('Bearer ', '');

    if (accessToken) {
      // Deactivate session in database
      const { error: updateError } = await supabase
        .from('user_sessions')
        .update({ 
          is_active: false,
          last_accessed: new Date().toISOString()
        })
        .eq('session_token', accessToken);

      if (updateError) {
        console.error('Session deactivation error:', updateError);
      }
    }

    // Sign out from Supabase
    const { error: signOutError } = await supabase.auth.signOut();
    
    if (signOutError) {
      console.error('Supabase sign out error:', signOutError);
    }

    // Log logout event
    await supabase.rpc('log_audit_event', {
      p_user_id: session.user.id,
      p_action: 'logout_success',
      p_resource: 'authentication',
      p_ip_address: clientIP,
      p_user_agent: userAgent,
      p_success: true
    });

    // Clear cookies
    const cookieOptions = {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict' as const,
      maxAge: 0, // Expire immediately
      path: '/'
    };

    response.cookies.set('access_token', '', cookieOptions);
    response.cookies.set('refresh_token', '', cookieOptions);

    return NextResponse.json({
      success: true,
      message: 'Logged out successfully'
    });

  } catch (error) {
    console.error('Logout error:', error);
    
    // Log error
    const clientIP = getClientIP(request);
    const userAgent = getUserAgent(request);
    
    try {
      const supabase = createRouteHandlerClient(request, response);
      await supabase.rpc('log_audit_event', {
        p_user_id: null,
        p_action: 'logout_error',
        p_resource: 'authentication',
        p_ip_address: clientIP,
        p_user_agent: userAgent,
        p_success: false,
        p_error_message: error instanceof Error ? error.message : 'Unknown error'
      });
    } catch (logError) {
      console.error('Failed to log error:', logError);
    }

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
