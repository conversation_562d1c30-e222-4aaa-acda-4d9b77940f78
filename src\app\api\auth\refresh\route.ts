import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@/lib/supabase';
import { 
  verifyRefreshToken, 
  generateAccessToken, 
  generateRefreshToken,
  getClientIP,
  getUserAgent
} from '@/utils/security';

export async function POST(request: NextRequest) {
  const response = NextResponse.next();
  
  try {
    // Get refresh token from cookies or body
    const refreshToken = request.cookies.get('refresh_token')?.value;
    
    if (!refreshToken) {
      return NextResponse.json(
        { success: false, error: 'Refresh token not provided' },
        { status: 401 }
      );
    }

    // Verify refresh token
    const payload = verifyRefreshToken(refreshToken);
    if (!payload) {
      return NextResponse.json(
        { success: false, error: 'Invalid refresh token' },
        { status: 401 }
      );
    }

    // Get client information
    const clientIP = getClientIP(request);
    const userAgent = getUserAgent(request);
    
    // Create Supabase client
    const supabase = createRouteHandlerClient(request, response);
    
    // Verify session exists and is active
    const { data: session, error: sessionError } = await supabase
      .from('user_sessions')
      .select('*')
      .eq('refresh_token', refreshToken)
      .eq('is_active', true)
      .single();

    if (sessionError || !session) {
      return NextResponse.json(
        { success: false, error: 'Session not found or expired' },
        { status: 401 }
      );
    }

    // Check if session has expired
    if (new Date(session.expires_at) < new Date()) {
      // Deactivate expired session
      await supabase
        .from('user_sessions')
        .update({ is_active: false })
        .eq('id', session.id);

      return NextResponse.json(
        { success: false, error: 'Session expired' },
        { status: 401 }
      );
    }

    // Get user details
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id, email, role, is_active')
      .eq('id', payload.userId)
      .single();

    if (userError || !user || !user.is_active) {
      return NextResponse.json(
        { success: false, error: 'User not found or inactive' },
        { status: 401 }
      );
    }

    // Generate new tokens
    const tokenPayload = {
      userId: user.id,
      email: user.email,
      role: user.role
    };

    const newAccessToken = generateAccessToken(tokenPayload);
    const newRefreshToken = generateRefreshToken(tokenPayload);

    // Update session with new tokens
    const { error: updateError } = await supabase
      .from('user_sessions')
      .update({
        session_token: newAccessToken,
        refresh_token: newRefreshToken,
        last_accessed: new Date().toISOString(),
        ip_address: clientIP,
        user_agent: userAgent
      })
      .eq('id', session.id);

    if (updateError) {
      console.error('Session update error:', updateError);
      return NextResponse.json(
        { success: false, error: 'Failed to update session' },
        { status: 500 }
      );
    }

    // Log token refresh
    await supabase.rpc('log_audit_event', {
      p_user_id: user.id,
      p_action: 'token_refresh',
      p_resource: 'authentication',
      p_resource_id: session.id,
      p_ip_address: clientIP,
      p_user_agent: userAgent,
      p_success: true
    });

    // Set new cookies
    const cookieOptions = {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict' as const,
      maxAge: 24 * 60 * 60, // 24 hours for access token
      path: '/'
    };

    response.cookies.set('access_token', newAccessToken, cookieOptions);
    response.cookies.set('refresh_token', newRefreshToken, {
      ...cookieOptions,
      maxAge: 7 * 24 * 60 * 60 // 7 days for refresh token
    });

    return NextResponse.json({
      success: true,
      message: 'Tokens refreshed successfully',
      tokens: {
        accessToken: newAccessToken,
        refreshToken: newRefreshToken,
        expiresAt: new Date(Date.now() + 15 * 60 * 1000).toISOString() // 15 minutes
      }
    });

  } catch (error) {
    console.error('Token refresh error:', error);
    
    // Log error
    const clientIP = getClientIP(request);
    const userAgent = getUserAgent(request);
    
    try {
      const supabase = createRouteHandlerClient(request, response);
      await supabase.rpc('log_audit_event', {
        p_user_id: null,
        p_action: 'token_refresh_error',
        p_resource: 'authentication',
        p_ip_address: clientIP,
        p_user_agent: userAgent,
        p_success: false,
        p_error_message: error instanceof Error ? error.message : 'Unknown error'
      });
    } catch (logError) {
      console.error('Failed to log error:', logError);
    }

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
