import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@/lib/supabase';
import { 
  hashPassword, 
  getClientIP,
  getUserAgent,
  validatePasswordStrength,
  generateSecureRandomString
} from '@/utils/security';
import { registerSchema } from '@/utils/validation';

export async function POST(request: NextRequest) {
  const response = NextResponse.next();
  
  try {
    // Parse request body
    const body = await request.json();
    
    // Validate input
    const validationResult = registerSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid input',
          details: validationResult.error.errors 
        },
        { status: 400 }
      );
    }

    const { 
      email, 
      password, 
      firstName, 
      lastName, 
      dateOfBirth, 
      phoneNumber 
    } = validationResult.data;
    
    // Additional password strength validation
    const passwordValidation = validatePasswordStrength(password);
    if (!passwordValidation.isValid) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Password does not meet security requirements',
          details: passwordValidation.errors 
        },
        { status: 400 }
      );
    }
    
    // Get client information
    const clientIP = getClientIP(request);
    const userAgent = getUserAgent(request);
    
    // Create Supabase client
    const supabase = createRouteHandlerClient(request, response);
    
    // Check if user already exists
    const { data: existingUser } = await supabase
      .from('users')
      .select('id, email')
      .eq('email', email)
      .single();

    if (existingUser) {
      // Log registration attempt with existing email
      await supabase.rpc('log_audit_event', {
        p_user_id: null,
        p_action: 'registration_failed',
        p_resource: 'user',
        p_ip_address: clientIP,
        p_user_agent: userAgent,
        p_success: false,
        p_error_message: 'Email already exists'
      });

      return NextResponse.json(
        { success: false, error: 'Email already registered' },
        { status: 409 }
      );
    }

    // Hash password
    const hashedPassword = await hashPassword(password);
    
    // Create user
    const { data: newUser, error: userError } = await supabase
      .from('users')
      .insert({
        email,
        password_hash: hashedPassword,
        first_name: firstName,
        last_name: lastName,
        phone_number: phoneNumber,
        date_of_birth: dateOfBirth,
        role: 'user',
        is_active: true,
        email_verified: false,
        phone_verified: false,
        two_factor_enabled: false
      })
      .select()
      .single();

    if (userError) {
      console.error('User creation error:', userError);
      
      await supabase.rpc('log_audit_event', {
        p_user_id: null,
        p_action: 'registration_failed',
        p_resource: 'user',
        p_ip_address: clientIP,
        p_user_agent: userAgent,
        p_success: false,
        p_error_message: userError.message
      });

      return NextResponse.json(
        { success: false, error: 'Failed to create user account' },
        { status: 500 }
      );
    }

    // Create user profile
    const { error: profileError } = await supabase
      .from('user_profiles')
      .insert({
        user_id: newUser.id,
        preferences: {
          notifications: {
            email: true,
            sms: false,
            push: true
          },
          security: {
            loginNotifications: true,
            transactionAlerts: true
          }
        }
      });

    if (profileError) {
      console.error('Profile creation error:', profileError);
      // Continue even if profile creation fails
    }

    // Create default checking account
    const accountNumber = await generateAccountNumber('checking');
    const { data: newAccount, error: accountError } = await supabase
      .from('accounts')
      .insert({
        user_id: newUser.id,
        account_number: accountNumber,
        account_type: 'checking',
        balance: 0.00,
        currency: 'USD',
        daily_limit: 1000.00,
        monthly_limit: 25000.00,
        is_active: true
      })
      .select()
      .single();

    if (accountError) {
      console.error('Account creation error:', accountError);
      // Continue even if account creation fails
    }

    // Generate email verification token
    const verificationToken = generateSecureRandomString(32);
    
    // Store verification token (in production, you'd send this via email)
    const { error: tokenError } = await supabase
      .from('user_sessions')
      .insert({
        user_id: newUser.id,
        session_token: verificationToken,
        refresh_token: generateSecureRandomString(32),
        ip_address: clientIP,
        user_agent: userAgent,
        expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours
        is_active: false // Not active until email is verified
      });

    // Log successful registration
    await supabase.rpc('log_audit_event', {
      p_user_id: newUser.id,
      p_action: 'registration_success',
      p_resource: 'user',
      p_resource_id: newUser.id,
      p_ip_address: clientIP,
      p_user_agent: userAgent,
      p_success: true,
      p_metadata: {
        account_created: newAccount ? true : false,
        verification_token_sent: tokenError ? false : true
      }
    });

    // Log security event for new user registration
    await supabase.rpc('log_security_event', {
      p_user_id: newUser.id,
      p_event_type: 'new_user_registration',
      p_severity: 'low',
      p_description: 'New user account created',
      p_ip_address: clientIP,
      p_user_agent: userAgent,
      p_metadata: {
        email: email,
        registration_source: 'web'
      }
    });

    // Return success response (without sensitive data)
    return NextResponse.json({
      success: true,
      message: 'Account created successfully. Please check your email to verify your account.',
      user: {
        id: newUser.id,
        email: newUser.email,
        firstName: newUser.first_name,
        lastName: newUser.last_name,
        emailVerified: newUser.email_verified
      },
      account: newAccount ? {
        id: newAccount.id,
        accountNumber: newAccount.account_number,
        accountType: newAccount.account_type,
        balance: newAccount.balance
      } : null,
      verificationRequired: true
    }, { status: 201 });

  } catch (error) {
    console.error('Registration error:', error);
    
    // Log error
    const clientIP = getClientIP(request);
    const userAgent = getUserAgent(request);
    
    try {
      const supabase = createRouteHandlerClient(request, response);
      await supabase.rpc('log_audit_event', {
        p_user_id: null,
        p_action: 'registration_error',
        p_resource: 'user',
        p_ip_address: clientIP,
        p_user_agent: userAgent,
        p_success: false,
        p_error_message: error instanceof Error ? error.message : 'Unknown error'
      });
    } catch (logError) {
      console.error('Failed to log error:', logError);
    }

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Helper function to generate account number
async function generateAccountNumber(accountType: 'checking' | 'savings' | 'investment'): Promise<string> {
  const prefix = accountType === 'checking' ? '100' : accountType === 'savings' ? '200' : '300';
  const randomPart = Math.floor(Math.random() * ***********).toString().padStart(10, '0');
  const checkDigit = calculateCheckDigit(prefix + randomPart);
  
  return prefix + randomPart + checkDigit;
}

function calculateCheckDigit(accountNumber: string): string {
  const sum = accountNumber
    .split('')
    .reduce((acc, digit, index) => {
      const num = parseInt(digit);
      return acc + (index % 2 === 0 ? num : num * 2 > 9 ? num * 2 - 9 : num * 2);
    }, 0);
  
  return ((10 - (sum % 10)) % 10).toString();
}
