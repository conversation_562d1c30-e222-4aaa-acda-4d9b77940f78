import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@/lib/supabase';
import { getClientIP, getUserAgent } from '@/utils/security';
import { billPaymentSchema, validateInput } from '@/utils/validation';

// GET /api/bills - Get user's payees and bill payment history
export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') || 'payees'; // 'payees' or 'payments'
    const page = parseInt(searchParams.get('page') || '1');
    const limit = Math.min(parseInt(searchParams.get('limit') || '20'), 100);
    
    // Get client information
    const clientIP = getClientIP(request);
    const userAgent = getUserAgent(request);
    
    // Create Supabase client
    const supabase = createRouteHandlerClient(request, NextResponse.next());
    
    // Get current session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    if (type === 'payees') {
      // Get user's payees
      const { data: payees, error: payeesError } = await supabase
        .from('payees')
        .select(`
          id,
          name,
          account_number,
          routing_number,
          category,
          is_active,
          created_at,
          updated_at
        `)
        .eq('user_id', session.user.id)
        .eq('is_active', true)
        .order('name', { ascending: true });

      if (payeesError) {
        console.error('Payees fetch error:', payeesError);
        return NextResponse.json(
          { success: false, error: 'Failed to fetch payees' },
          { status: 500 }
        );
      }

      // Mask sensitive information
      const maskedPayees = payees.map(payee => ({
        ...payee,
        account_number: payee.account_number ? `****${payee.account_number.slice(-4)}` : null,
        routing_number: payee.routing_number ? `****${payee.routing_number.slice(-4)}` : null
      }));

      return NextResponse.json({
        success: true,
        data: maskedPayees,
        timestamp: new Date().toISOString()
      });

    } else if (type === 'payments') {
      // Get bill payment history
      const offset = (page - 1) * limit;
      
      const { data: payments, error: paymentsError, count } = await supabase
        .from('bill_payments')
        .select(`
          id,
          amount,
          due_date,
          memo,
          confirmation_number,
          created_at,
          payees!inner(
            name,
            category
          ),
          accounts!inner(
            account_number,
            account_type,
            user_id
          ),
          transactions!inner(
            reference_number,
            status,
            processed_date
          )
        `, { count: 'exact' })
        .eq('accounts.user_id', session.user.id)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (paymentsError) {
        console.error('Bill payments fetch error:', paymentsError);
        return NextResponse.json(
          { success: false, error: 'Failed to fetch bill payments' },
          { status: 500 }
        );
      }

      // Format payments for response
      const formattedPayments = payments.map(payment => ({
        id: payment.id,
        amount: payment.amount,
        dueDate: payment.due_date,
        memo: payment.memo,
        confirmationNumber: payment.confirmation_number,
        createdAt: payment.created_at,
        payeeName: payment.payees.name,
        payeeCategory: payment.payees.category,
        accountNumber: `****${payment.accounts.account_number.slice(-4)}`,
        accountType: payment.accounts.account_type,
        referenceNumber: payment.transactions.reference_number,
        status: payment.transactions.status,
        processedDate: payment.transactions.processed_date
      }));

      // Calculate pagination
      const totalPages = Math.ceil((count || 0) / limit);

      return NextResponse.json({
        success: true,
        data: formattedPayments,
        pagination: {
          page,
          limit,
          total: count || 0,
          totalPages,
          hasNextPage: page < totalPages,
          hasPreviousPage: page > 1
        },
        timestamp: new Date().toISOString()
      });
    }

    return NextResponse.json(
      { success: false, error: 'Invalid type parameter' },
      { status: 400 }
    );

  } catch (error) {
    console.error('Bills API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/bills - Process bill payment
export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const body = await request.json();
    
    // Validate input
    const validation = validateInput(billPaymentSchema, body);
    if (!validation.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid input',
          details: validation.errors 
        },
        { status: 400 }
      );
    }

    const { accountId, payeeId, amount, dueDate, memo } = validation.data;
    
    // Get client information
    const clientIP = getClientIP(request);
    const userAgent = getUserAgent(request);
    
    // Create Supabase client
    const supabase = createRouteHandlerClient(request, NextResponse.next());
    
    // Get current session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Verify account ownership
    const { data: account, error: accountError } = await supabase
      .from('accounts')
      .select('id, user_id, account_number, account_type, balance, is_active')
      .eq('id', accountId)
      .eq('user_id', session.user.id)
      .single();

    if (accountError || !account || !account.is_active) {
      return NextResponse.json(
        { success: false, error: 'Account not found or inactive' },
        { status: 404 }
      );
    }

    // Verify payee ownership
    const { data: payee, error: payeeError } = await supabase
      .from('payees')
      .select('id, user_id, name, category, is_active')
      .eq('id', payeeId)
      .eq('user_id', session.user.id)
      .single();

    if (payeeError || !payee || !payee.is_active) {
      return NextResponse.json(
        { success: false, error: 'Payee not found or inactive' },
        { status: 404 }
      );
    }

    // Check account balance and limits
    const { data: limitCheck } = await supabase.rpc('check_account_limits', {
      p_account_id: accountId,
      p_amount: amount,
      p_transaction_type: 'debit'
    });

    if (!limitCheck || !(limitCheck as any).valid) {
      const errorMessage = (limitCheck as any)?.error || 'Payment amount exceeds limits';
      
      await supabase.rpc('log_audit_event', {
        p_user_id: session.user.id,
        p_action: 'bill_payment_limit_exceeded',
        p_resource: 'bill_payment',
        p_ip_address: clientIP,
        p_user_agent: userAgent,
        p_success: false,
        p_error_message: errorMessage,
        p_metadata: {
          account_id: accountId,
          payee_id: payeeId,
          amount: amount
        }
      });

      return NextResponse.json(
        { success: false, error: errorMessage },
        { status: 400 }
      );
    }

    // Check for suspicious activity
    const { data: isSuspicious } = await supabase.rpc('detect_suspicious_activity', {
      p_user_id: session.user.id,
      p_ip_address: clientIP,
      p_action: 'bill_payment_request'
    });

    if (isSuspicious) {
      await supabase.rpc('log_security_event', {
        p_user_id: session.user.id,
        p_event_type: 'suspicious_bill_payment_activity',
        p_severity: 'high',
        p_description: 'Suspicious bill payment activity detected',
        p_ip_address: clientIP,
        p_user_agent: userAgent,
        p_metadata: {
          account_id: accountId,
          payee_id: payeeId,
          amount: amount
        }
      });
    }

    // Generate reference number and confirmation number
    const referenceNumber = `BILL${Date.now()}${Math.random().toString(36).substring(2, 8).toUpperCase()}`;
    const confirmationNumber = `CONF${Date.now()}${Math.random().toString(36).substring(2, 6).toUpperCase()}`;

    // Process the payment transaction
    const { data: transactionId, error: transactionError } = await supabase.rpc('process_transaction', {
      p_account_id: accountId,
      p_transaction_type: 'debit',
      p_amount: amount,
      p_description: `Bill Payment - ${payee.name}`,
      p_reference_number: referenceNumber,
      p_merchant_name: payee.name,
      p_merchant_category: payee.category,
      p_metadata: {
        payment_type: 'bill_payment',
        payee_id: payeeId,
        due_date: dueDate,
        memo: memo,
        confirmation_number: confirmationNumber,
        ip_address: clientIP,
        user_agent: userAgent
      }
    });

    if (transactionError) {
      console.error('Bill payment transaction error:', transactionError);
      
      await supabase.rpc('log_audit_event', {
        p_user_id: session.user.id,
        p_action: 'bill_payment_failed',
        p_resource: 'bill_payment',
        p_ip_address: clientIP,
        p_user_agent: userAgent,
        p_success: false,
        p_error_message: transactionError.message,
        p_metadata: {
          account_id: accountId,
          payee_id: payeeId,
          amount: amount
        }
      });

      return NextResponse.json(
        { success: false, error: transactionError.message },
        { status: 400 }
      );
    }

    // Create bill payment record
    const { data: billPayment, error: billPaymentError } = await supabase
      .from('bill_payments')
      .insert({
        account_id: accountId,
        payee_id: payeeId,
        transaction_id: transactionId,
        amount: amount,
        due_date: dueDate,
        memo: memo,
        confirmation_number: confirmationNumber
      })
      .select()
      .single();

    if (billPaymentError) {
      console.error('Bill payment record creation error:', billPaymentError);
      // Transaction was processed, but record creation failed
      // In production, you might want to implement compensation logic
    }

    // Log successful bill payment
    await supabase.rpc('log_audit_event', {
      p_user_id: session.user.id,
      p_action: 'bill_payment_completed',
      p_resource: 'bill_payment',
      p_resource_id: billPayment?.id || transactionId,
      p_ip_address: clientIP,
      p_user_agent: userAgent,
      p_success: true,
      p_metadata: {
        account_id: accountId,
        account_number: account.account_number,
        payee_id: payeeId,
        payee_name: payee.name,
        amount: amount,
        confirmation_number: confirmationNumber,
        reference_number: referenceNumber
      }
    });

    // Send response
    return NextResponse.json({
      success: true,
      message: 'Bill payment processed successfully',
      data: {
        paymentId: billPayment?.id,
        transactionId: transactionId,
        account: {
          id: account.id,
          accountNumber: `****${account.account_number.slice(-4)}`,
          accountType: account.account_type
        },
        payee: {
          id: payee.id,
          name: payee.name,
          category: payee.category
        },
        amount: amount,
        dueDate: dueDate,
        memo: memo,
        confirmationNumber: confirmationNumber,
        referenceNumber: referenceNumber,
        status: 'completed',
        processedAt: new Date().toISOString()
      },
      timestamp: new Date().toISOString()
    }, { status: 201 });

  } catch (error) {
    console.error('Bill payment API error:', error);
    
    // Log error
    const clientIP = getClientIP(request);
    const userAgent = getUserAgent(request);
    
    try {
      const supabase = createRouteHandlerClient(request, NextResponse.next());
      const { data: { session } } = await supabase.auth.getSession();
      
      await supabase.rpc('log_audit_event', {
        p_user_id: session?.user?.id || null,
        p_action: 'bill_payment_error',
        p_resource: 'bill_payment',
        p_ip_address: clientIP,
        p_user_agent: userAgent,
        p_success: false,
        p_error_message: error instanceof Error ? error.message : 'Unknown error'
      });
    } catch (logError) {
      console.error('Failed to log error:', logError);
    }

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
