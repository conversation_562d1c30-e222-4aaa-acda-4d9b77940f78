import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@/lib/supabase';
import { getClientIP, getUserAgent } from '@/utils/security';
import { transactionFilterSchema, validateInput } from '@/utils/validation';

// GET /api/transactions - Get user's transactions with filtering
export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const { searchParams } = new URL(request.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    
    // Convert string numbers to actual numbers for validation
    if (queryParams.page) queryParams.page = parseInt(queryParams.page);
    if (queryParams.limit) queryParams.limit = parseInt(queryParams.limit);
    if (queryParams.minAmount) queryParams.minAmount = parseFloat(queryParams.minAmount);
    if (queryParams.maxAmount) queryParams.maxAmount = parseFloat(queryParams.maxAmount);
    
    // Validate query parameters
    const validation = validateInput(transactionFilterSchema, queryParams);
    if (!validation.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid query parameters',
          details: validation.errors 
        },
        { status: 400 }
      );
    }

    const {
      accountId,
      startDate,
      endDate,
      minAmount,
      maxAmount,
      transactionType,
      status,
      page,
      limit
    } = validation.data;
    
    // Get client information
    const clientIP = getClientIP(request);
    const userAgent = getUserAgent(request);
    
    // Create Supabase client
    const supabase = createRouteHandlerClient(request, NextResponse.next());
    
    // Get current session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Build query for transactions
    let query = supabase
      .from('transactions')
      .select(`
        id,
        account_id,
        transaction_type,
        amount,
        balance_after,
        description,
        reference_number,
        status,
        merchant_name,
        merchant_category,
        processed_date,
        created_at,
        accounts!inner(
          account_number,
          account_type,
          user_id
        )
      `)
      .eq('accounts.user_id', session.user.id);

    // Apply filters
    if (accountId) {
      query = query.eq('account_id', accountId);
    }
    
    if (startDate) {
      query = query.gte('created_at', startDate);
    }
    
    if (endDate) {
      query = query.lte('created_at', endDate);
    }
    
    if (minAmount !== undefined) {
      query = query.gte('amount', minAmount);
    }
    
    if (maxAmount !== undefined) {
      query = query.lte('amount', maxAmount);
    }
    
    if (transactionType) {
      query = query.eq('transaction_type', transactionType);
    }
    
    if (status) {
      query = query.eq('status', status);
    }

    // Get total count for pagination
    const { count } = await query.select('*', { count: 'exact', head: true });
    
    // Apply pagination and ordering
    const offset = (page - 1) * limit;
    query = query
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    const { data: transactions, error: transactionsError } = await query;

    if (transactionsError) {
      console.error('Transactions fetch error:', transactionsError);
      
      await supabase.rpc('log_audit_event', {
        p_user_id: session.user.id,
        p_action: 'transactions_fetch_failed',
        p_resource: 'transaction',
        p_ip_address: clientIP,
        p_user_agent: userAgent,
        p_success: false,
        p_error_message: transactionsError.message
      });

      return NextResponse.json(
        { success: false, error: 'Failed to fetch transactions' },
        { status: 500 }
      );
    }

    // Format transactions for response
    const formattedTransactions = transactions.map(tx => ({
      id: tx.id,
      accountId: tx.account_id,
      accountNumber: tx.accounts.account_number,
      accountType: tx.accounts.account_type,
      transactionType: tx.transaction_type,
      amount: tx.amount,
      balanceAfter: tx.balance_after,
      description: tx.description,
      referenceNumber: tx.reference_number,
      status: tx.status,
      merchantName: tx.merchant_name,
      merchantCategory: tx.merchant_category,
      processedDate: tx.processed_date,
      createdAt: tx.created_at
    }));

    // Calculate pagination info
    const totalPages = Math.ceil((count || 0) / limit);
    const hasNextPage = page < totalPages;
    const hasPreviousPage = page > 1;

    // Log successful access
    await supabase.rpc('log_audit_event', {
      p_user_id: session.user.id,
      p_action: 'transactions_viewed',
      p_resource: 'transaction',
      p_ip_address: clientIP,
      p_user_agent: userAgent,
      p_success: true,
      p_metadata: { 
        transactions_count: transactions.length,
        filters_applied: {
          accountId,
          startDate,
          endDate,
          transactionType,
          status
        }
      }
    });

    return NextResponse.json({
      success: true,
      data: formattedTransactions,
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages,
        hasNextPage,
        hasPreviousPage
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Transactions API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/transactions - Create a new transaction (for testing purposes)
export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const body = await request.json();
    const { accountId, transactionType, amount, description, merchantName, merchantCategory } = body;
    
    // Basic validation
    if (!accountId || !transactionType || !amount || !description) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }

    if (amount <= 0) {
      return NextResponse.json(
        { success: false, error: 'Amount must be positive' },
        { status: 400 }
      );
    }
    
    // Get client information
    const clientIP = getClientIP(request);
    const userAgent = getUserAgent(request);
    
    // Create Supabase client
    const supabase = createRouteHandlerClient(request, NextResponse.next());
    
    // Get current session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Verify account ownership
    const { data: account, error: accountError } = await supabase
      .from('accounts')
      .select('id, user_id, account_type, is_active')
      .eq('id', accountId)
      .eq('user_id', session.user.id)
      .single();

    if (accountError || !account || !account.is_active) {
      return NextResponse.json(
        { success: false, error: 'Account not found or inactive' },
        { status: 404 }
      );
    }

    // Generate reference number
    const referenceNumber = `TXN${Date.now()}${Math.random().toString(36).substring(2, 8).toUpperCase()}`;

    // Process transaction using database function
    const { data: transactionId, error: transactionError } = await supabase.rpc('process_transaction', {
      p_account_id: accountId,
      p_transaction_type: transactionType,
      p_amount: amount,
      p_description: description,
      p_reference_number: referenceNumber,
      p_merchant_name: merchantName || null,
      p_merchant_category: merchantCategory || null,
      p_metadata: {
        created_via: 'api',
        ip_address: clientIP,
        user_agent: userAgent
      }
    });

    if (transactionError) {
      console.error('Transaction processing error:', transactionError);
      
      await supabase.rpc('log_audit_event', {
        p_user_id: session.user.id,
        p_action: 'transaction_creation_failed',
        p_resource: 'transaction',
        p_ip_address: clientIP,
        p_user_agent: userAgent,
        p_success: false,
        p_error_message: transactionError.message
      });

      return NextResponse.json(
        { success: false, error: transactionError.message },
        { status: 400 }
      );
    }

    // Get the created transaction
    const { data: newTransaction, error: fetchError } = await supabase
      .from('transactions')
      .select('*')
      .eq('id', transactionId)
      .single();

    if (fetchError) {
      console.error('Transaction fetch error:', fetchError);
      return NextResponse.json(
        { success: false, error: 'Transaction created but failed to fetch details' },
        { status: 500 }
      );
    }

    // Log successful transaction creation
    await supabase.rpc('log_audit_event', {
      p_user_id: session.user.id,
      p_action: 'transaction_created',
      p_resource: 'transaction',
      p_resource_id: transactionId,
      p_ip_address: clientIP,
      p_user_agent: userAgent,
      p_success: true,
      p_metadata: {
        transaction_type: transactionType,
        amount: amount,
        account_id: accountId
      }
    });

    return NextResponse.json({
      success: true,
      message: 'Transaction processed successfully',
      data: {
        id: newTransaction.id,
        accountId: newTransaction.account_id,
        transactionType: newTransaction.transaction_type,
        amount: newTransaction.amount,
        balanceAfter: newTransaction.balance_after,
        description: newTransaction.description,
        referenceNumber: newTransaction.reference_number,
        status: newTransaction.status,
        createdAt: newTransaction.created_at
      },
      timestamp: new Date().toISOString()
    }, { status: 201 });

  } catch (error) {
    console.error('Transaction creation API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
