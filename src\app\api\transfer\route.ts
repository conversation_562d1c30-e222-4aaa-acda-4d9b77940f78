import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@/lib/supabase';
import { getClientIP, getUserAgent } from '@/utils/security';
import { transferSchema, validateInput } from '@/utils/validation';

// POST /api/transfer - Process internal transfer between accounts
export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const body = await request.json();
    
    // Validate input
    const validation = validateInput(transferSchema, body);
    if (!validation.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid input',
          details: validation.errors 
        },
        { status: 400 }
      );
    }

    const { 
      fromAccountId, 
      toAccountId, 
      amount, 
      description, 
      transferType,
      scheduledDate 
    } = validation.data;
    
    // Get client information
    const clientIP = getClientIP(request);
    const userAgent = getUserAgent(request);
    
    // Create Supabase client
    const supabase = createRouteHandlerClient(request, NextResponse.next());
    
    // Get current session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Verify both accounts exist and belong to the user
    const { data: fromAccount, error: fromAccountError } = await supabase
      .from('accounts')
      .select('id, user_id, account_number, account_type, balance, is_active, daily_limit')
      .eq('id', fromAccountId)
      .eq('user_id', session.user.id)
      .single();

    if (fromAccountError || !fromAccount || !fromAccount.is_active) {
      await supabase.rpc('log_security_event', {
        p_user_id: session.user.id,
        p_event_type: 'invalid_account_access',
        p_severity: 'medium',
        p_description: 'Attempted transfer from invalid or inactive account',
        p_ip_address: clientIP,
        p_user_agent: userAgent,
        p_metadata: { attempted_account_id: fromAccountId }
      });

      return NextResponse.json(
        { success: false, error: 'Source account not found or inactive' },
        { status: 404 }
      );
    }

    // For internal transfers, verify the destination account belongs to the same user
    let toAccount;
    if (transferType === 'internal') {
      const { data: toAccountData, error: toAccountError } = await supabase
        .from('accounts')
        .select('id, user_id, account_number, account_type, is_active')
        .eq('id', toAccountId)
        .eq('user_id', session.user.id)
        .single();

      if (toAccountError || !toAccountData || !toAccountData.is_active) {
        return NextResponse.json(
          { success: false, error: 'Destination account not found or inactive' },
          { status: 404 }
        );
      }
      toAccount = toAccountData;
    } else {
      // For external transfers, additional verification would be needed
      return NextResponse.json(
        { success: false, error: 'External transfers not yet implemented' },
        { status: 501 }
      );
    }

    // Check if scheduled transfer (not implemented in this demo)
    if (scheduledDate && new Date(scheduledDate) > new Date()) {
      return NextResponse.json(
        { success: false, error: 'Scheduled transfers not yet implemented' },
        { status: 501 }
      );
    }

    // Validate transfer amount limits
    const { data: limitCheck } = await supabase.rpc('check_account_limits', {
      p_account_id: fromAccountId,
      p_amount: amount,
      p_transaction_type: 'debit'
    });

    if (!limitCheck || !(limitCheck as any).valid) {
      const errorMessage = (limitCheck as any)?.error || 'Transfer amount exceeds limits';
      
      await supabase.rpc('log_audit_event', {
        p_user_id: session.user.id,
        p_action: 'transfer_limit_exceeded',
        p_resource: 'transfer',
        p_ip_address: clientIP,
        p_user_agent: userAgent,
        p_success: false,
        p_error_message: errorMessage,
        p_metadata: {
          from_account_id: fromAccountId,
          to_account_id: toAccountId,
          amount: amount
        }
      });

      return NextResponse.json(
        { success: false, error: errorMessage },
        { status: 400 }
      );
    }

    // Check for suspicious activity
    const { data: isSuspicious } = await supabase.rpc('detect_suspicious_activity', {
      p_user_id: session.user.id,
      p_ip_address: clientIP,
      p_action: 'transfer_request'
    });

    if (isSuspicious) {
      // Additional verification could be required here
      await supabase.rpc('log_security_event', {
        p_user_id: session.user.id,
        p_event_type: 'suspicious_transfer_activity',
        p_severity: 'high',
        p_description: 'Suspicious transfer activity detected',
        p_ip_address: clientIP,
        p_user_agent: userAgent,
        p_metadata: {
          from_account_id: fromAccountId,
          to_account_id: toAccountId,
          amount: amount
        }
      });
    }

    // Calculate transfer fees (simplified - in production this would be more complex)
    const transferFee = transferType === 'internal' ? 0 : amount * 0.001; // 0.1% for external

    // Process the transfer using database function
    const { data: transferId, error: transferError } = await supabase.rpc('process_internal_transfer', {
      p_from_account_id: fromAccountId,
      p_to_account_id: toAccountId,
      p_amount: amount,
      p_description: description,
      p_fees: transferFee
    });

    if (transferError) {
      console.error('Transfer processing error:', transferError);
      
      await supabase.rpc('log_audit_event', {
        p_user_id: session.user.id,
        p_action: 'transfer_failed',
        p_resource: 'transfer',
        p_ip_address: clientIP,
        p_user_agent: userAgent,
        p_success: false,
        p_error_message: transferError.message,
        p_metadata: {
          from_account_id: fromAccountId,
          to_account_id: toAccountId,
          amount: amount
        }
      });

      return NextResponse.json(
        { success: false, error: transferError.message },
        { status: 400 }
      );
    }

    // Get transfer details
    const { data: transferDetails, error: transferFetchError } = await supabase
      .from('transfers')
      .select(`
        id,
        from_account_id,
        to_account_id,
        transfer_type,
        fees,
        created_at,
        from_transaction:from_transaction_id(
          id,
          reference_number,
          amount,
          status
        ),
        to_transaction:to_transaction_id(
          id,
          reference_number,
          amount,
          status
        )
      `)
      .eq('id', transferId)
      .single();

    if (transferFetchError) {
      console.error('Transfer fetch error:', transferFetchError);
    }

    // Log successful transfer
    await supabase.rpc('log_audit_event', {
      p_user_id: session.user.id,
      p_action: 'transfer_completed',
      p_resource: 'transfer',
      p_resource_id: transferId,
      p_ip_address: clientIP,
      p_user_agent: userAgent,
      p_success: true,
      p_metadata: {
        from_account_id: fromAccountId,
        from_account_number: fromAccount.account_number,
        to_account_id: toAccountId,
        to_account_number: toAccount.account_number,
        amount: amount,
        fees: transferFee,
        transfer_type: transferType
      }
    });

    // Send response
    return NextResponse.json({
      success: true,
      message: 'Transfer completed successfully',
      data: {
        transferId: transferId,
        fromAccount: {
          id: fromAccount.id,
          accountNumber: fromAccount.account_number,
          accountType: fromAccount.account_type
        },
        toAccount: {
          id: toAccount.id,
          accountNumber: toAccount.account_number,
          accountType: toAccount.account_type
        },
        amount: amount,
        fees: transferFee,
        description: description,
        transferType: transferType,
        status: 'completed',
        referenceNumber: transferDetails?.from_transaction?.reference_number,
        createdAt: transferDetails?.created_at || new Date().toISOString()
      },
      timestamp: new Date().toISOString()
    }, { status: 201 });

  } catch (error) {
    console.error('Transfer API error:', error);
    
    // Log error
    const clientIP = getClientIP(request);
    const userAgent = getUserAgent(request);
    
    try {
      const supabase = createRouteHandlerClient(request, NextResponse.next());
      const { data: { session } } = await supabase.auth.getSession();
      
      await supabase.rpc('log_audit_event', {
        p_user_id: session?.user?.id || null,
        p_action: 'transfer_error',
        p_resource: 'transfer',
        p_ip_address: clientIP,
        p_user_agent: userAgent,
        p_success: false,
        p_error_message: error instanceof Error ? error.message : 'Unknown error'
      });
    } catch (logError) {
      console.error('Failed to log error:', logError);
    }

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// GET /api/transfer - Get user's transfer history
export async function GET(request: NextRequest) {
  try {
    // Get query parameters for pagination
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = Math.min(parseInt(searchParams.get('limit') || '20'), 100);
    
    // Get client information
    const clientIP = getClientIP(request);
    const userAgent = getUserAgent(request);
    
    // Create Supabase client
    const supabase = createRouteHandlerClient(request, NextResponse.next());
    
    // Get current session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user's transfers
    const offset = (page - 1) * limit;
    
    const { data: transfers, error: transfersError, count } = await supabase
      .from('transfers')
      .select(`
        id,
        transfer_type,
        fees,
        created_at,
        from_account:from_account_id(
          id,
          account_number,
          account_type,
          user_id
        ),
        to_account:to_account_id(
          id,
          account_number,
          account_type,
          user_id
        ),
        from_transaction:from_transaction_id(
          id,
          amount,
          description,
          reference_number,
          status
        ),
        to_transaction:to_transaction_id(
          id,
          amount,
          description,
          reference_number,
          status
        )
      `, { count: 'exact' })
      .or(`from_account.user_id.eq.${session.user.id},to_account.user_id.eq.${session.user.id}`)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (transfersError) {
      console.error('Transfers fetch error:', transfersError);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch transfers' },
        { status: 500 }
      );
    }

    // Log access
    await supabase.rpc('log_audit_event', {
      p_user_id: session.user.id,
      p_action: 'transfers_viewed',
      p_resource: 'transfer',
      p_ip_address: clientIP,
      p_user_agent: userAgent,
      p_success: true,
      p_metadata: { transfers_count: transfers.length }
    });

    // Calculate pagination
    const totalPages = Math.ceil((count || 0) / limit);

    return NextResponse.json({
      success: true,
      data: transfers,
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Transfer history API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
