import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@/lib/supabase';
import { getClientIP, getUserAgent } from '@/utils/security';
import { updateProfileSchema, validateInput } from '@/utils/validation';

// GET /api/user/profile - Get user profile
export async function GET(request: NextRequest) {
  try {
    // Get client information
    const clientIP = getClientIP(request);
    const userAgent = getUserAgent(request);
    
    // Create Supabase client
    const supabase = createRouteHandlerClient(request, NextResponse.next());
    
    // Get current session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user details
    const { data: user, error: userError } = await supabase
      .from('users')
      .select(`
        id,
        email,
        first_name,
        last_name,
        phone_number,
        date_of_birth,
        role,
        is_active,
        email_verified,
        phone_verified,
        two_factor_enabled,
        last_login,
        created_at,
        updated_at
      `)
      .eq('id', session.user.id)
      .single();

    if (userError || !user) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }

    // Get user profile details
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select(`
        address_street,
        address_city,
        address_state,
        address_zip,
        address_country,
        occupation,
        annual_income,
        profile_picture_url,
        preferences,
        created_at,
        updated_at
      `)
      .eq('user_id', session.user.id)
      .single();

    // Profile might not exist yet, so don't treat as error
    const profileData = profile || {};

    // Get account summary
    const { data: accounts, error: accountsError } = await supabase
      .from('accounts')
      .select('id, account_type, balance, currency, is_active')
      .eq('user_id', session.user.id)
      .eq('is_active', true);

    const accountSummary = accounts || [];

    // Log profile access
    await supabase.rpc('log_audit_event', {
      p_user_id: session.user.id,
      p_action: 'profile_viewed',
      p_resource: 'user_profile',
      p_resource_id: session.user.id,
      p_ip_address: clientIP,
      p_user_agent: userAgent,
      p_success: true
    });

    return NextResponse.json({
      success: true,
      data: {
        user: {
          id: user.id,
          email: user.email,
          firstName: user.first_name,
          lastName: user.last_name,
          phoneNumber: user.phone_number,
          dateOfBirth: user.date_of_birth,
          role: user.role,
          isActive: user.is_active,
          emailVerified: user.email_verified,
          phoneVerified: user.phone_verified,
          twoFactorEnabled: user.two_factor_enabled,
          lastLogin: user.last_login,
          createdAt: user.created_at,
          updatedAt: user.updated_at
        },
        profile: {
          address: {
            street: profileData.address_street,
            city: profileData.address_city,
            state: profileData.address_state,
            zipCode: profileData.address_zip,
            country: profileData.address_country
          },
          occupation: profileData.occupation,
          annualIncome: profileData.annual_income,
          profilePictureUrl: profileData.profile_picture_url,
          preferences: profileData.preferences || {},
          createdAt: profileData.created_at,
          updatedAt: profileData.updated_at
        },
        accountSummary: accountSummary.map(account => ({
          id: account.id,
          accountType: account.account_type,
          balance: account.balance,
          currency: account.currency
        }))
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Profile fetch error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT /api/user/profile - Update user profile
export async function PUT(request: NextRequest) {
  try {
    // Parse request body
    const body = await request.json();
    
    // Validate input
    const validation = validateInput(updateProfileSchema, body);
    if (!validation.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid input',
          details: validation.errors 
        },
        { status: 400 }
      );
    }

    const { firstName, lastName, phoneNumber, address } = validation.data;
    
    // Get client information
    const clientIP = getClientIP(request);
    const userAgent = getUserAgent(request);
    
    // Create Supabase client
    const supabase = createRouteHandlerClient(request, NextResponse.next());
    
    // Get current session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get current user data for audit logging
    const { data: currentUser } = await supabase
      .from('users')
      .select('first_name, last_name, phone_number')
      .eq('id', session.user.id)
      .single();

    // Update user basic information
    const { data: updatedUser, error: userUpdateError } = await supabase
      .from('users')
      .update({
        first_name: firstName,
        last_name: lastName,
        phone_number: phoneNumber,
        updated_at: new Date().toISOString()
      })
      .eq('id', session.user.id)
      .select()
      .single();

    if (userUpdateError) {
      console.error('User update error:', userUpdateError);
      
      await supabase.rpc('log_audit_event', {
        p_user_id: session.user.id,
        p_action: 'profile_update_failed',
        p_resource: 'user_profile',
        p_resource_id: session.user.id,
        p_ip_address: clientIP,
        p_user_agent: userAgent,
        p_success: false,
        p_error_message: userUpdateError.message
      });

      return NextResponse.json(
        { success: false, error: 'Failed to update user information' },
        { status: 500 }
      );
    }

    // Get current profile data for audit logging
    const { data: currentProfile } = await supabase
      .from('user_profiles')
      .select('address_street, address_city, address_state, address_zip, address_country')
      .eq('user_id', session.user.id)
      .single();

    // Update or insert user profile
    const profileData = {
      user_id: session.user.id,
      address_street: address.street,
      address_city: address.city,
      address_state: address.state,
      address_zip: address.zipCode,
      address_country: address.country,
      updated_at: new Date().toISOString()
    };

    const { data: updatedProfile, error: profileUpdateError } = await supabase
      .from('user_profiles')
      .upsert(profileData)
      .select()
      .single();

    if (profileUpdateError) {
      console.error('Profile update error:', profileUpdateError);
      
      await supabase.rpc('log_audit_event', {
        p_user_id: session.user.id,
        p_action: 'profile_update_failed',
        p_resource: 'user_profile',
        p_resource_id: session.user.id,
        p_ip_address: clientIP,
        p_user_agent: userAgent,
        p_success: false,
        p_error_message: profileUpdateError.message
      });

      return NextResponse.json(
        { success: false, error: 'Failed to update profile information' },
        { status: 500 }
      );
    }

    // Log successful profile update with old and new values
    await supabase.rpc('log_audit_event', {
      p_user_id: session.user.id,
      p_action: 'profile_updated',
      p_resource: 'user_profile',
      p_resource_id: session.user.id,
      p_ip_address: clientIP,
      p_user_agent: userAgent,
      p_success: true,
      p_old_values: {
        user: currentUser,
        profile: currentProfile
      },
      p_new_values: {
        user: {
          first_name: firstName,
          last_name: lastName,
          phone_number: phoneNumber
        },
        profile: {
          address_street: address.street,
          address_city: address.city,
          address_state: address.state,
          address_zip: address.zipCode,
          address_country: address.country
        }
      }
    });

    return NextResponse.json({
      success: true,
      message: 'Profile updated successfully',
      data: {
        user: {
          id: updatedUser.id,
          email: updatedUser.email,
          firstName: updatedUser.first_name,
          lastName: updatedUser.last_name,
          phoneNumber: updatedUser.phone_number,
          updatedAt: updatedUser.updated_at
        },
        profile: {
          address: {
            street: updatedProfile.address_street,
            city: updatedProfile.address_city,
            state: updatedProfile.address_state,
            zipCode: updatedProfile.address_zip,
            country: updatedProfile.address_country
          },
          updatedAt: updatedProfile.updated_at
        }
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Profile update error:', error);
    
    // Log error
    const clientIP = getClientIP(request);
    const userAgent = getUserAgent(request);
    
    try {
      const supabase = createRouteHandlerClient(request, NextResponse.next());
      const { data: { session } } = await supabase.auth.getSession();
      
      await supabase.rpc('log_audit_event', {
        p_user_id: session?.user?.id || null,
        p_action: 'profile_update_error',
        p_resource: 'user_profile',
        p_ip_address: clientIP,
        p_user_agent: userAgent,
        p_success: false,
        p_error_message: error instanceof Error ? error.message : 'Unknown error'
      });
    } catch (logError) {
      console.error('Failed to log error:', logError);
    }

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
