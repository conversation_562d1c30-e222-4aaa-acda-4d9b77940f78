'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { 
  CreditCard, 
  TrendingUp, 
  DollarSign, 
  Eye, 
  EyeOff, 
  Plus,
  ArrowUpRight,
  ArrowDownLeft,
  Calendar,
  <PERSON><PERSON>hart,
  BarChart3,
  Download,
  Filter
} from 'lucide-react';

interface Account {
  id: string;
  accountNumber: string;
  accountType: 'checking' | 'savings' | 'investment';
  balance: number;
  currency: string;
  dailyUsage: number;
  monthlyUsage: number;
  dailyRemaining: number;
  monthlyRemaining: number;
  dailyLimit: number;
  monthlyLimit: number;
  isActive: boolean;
  openedDate: string;
}

interface Transaction {
  id: string;
  accountId: string;
  accountNumber: string;
  accountType: string;
  transactionType: 'debit' | 'credit' | 'transfer';
  amount: number;
  balanceAfter: number;
  description: string;
  referenceNumber: string;
  status: string;
  merchantName?: string;
  merchantCategory?: string;
  processedDate?: string;
  createdAt: string;
}

interface AccountSummary {
  totalBalance: number;
  totalAccounts: number;
  monthlyIncome: number;
  monthlyExpenses: number;
  savingsRate: number;
}

export default function AccountDashboard() {
  const { user, isAuthenticated } = useAuth();
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [summary, setSummary] = useState<AccountSummary | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [balanceVisible, setBalanceVisible] = useState(true);
  const [selectedAccount, setSelectedAccount] = useState<string>('all');
  const [dateRange, setDateRange] = useState<string>('30');
  const [transactionFilter, setTransactionFilter] = useState<string>('all');

  useEffect(() => {
    if (isAuthenticated) {
      fetchAccountData();
    }
  }, [isAuthenticated, selectedAccount, dateRange, transactionFilter]);

  const fetchAccountData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch accounts
      const accountsResponse = await fetch('/api/accounts');
      if (!accountsResponse.ok) throw new Error('Failed to fetch accounts');
      
      const accountsData = await accountsResponse.json();
      if (accountsData.success) {
        setAccounts(accountsData.data);
        calculateSummary(accountsData.data);
      }

      // Fetch transactions with filters
      const transactionParams = new URLSearchParams({
        limit: '50',
        ...(selectedAccount !== 'all' && { accountId: selectedAccount }),
        ...(transactionFilter !== 'all' && { transactionType: transactionFilter }),
        ...(dateRange !== 'all' && { 
          startDate: new Date(Date.now() - parseInt(dateRange) * 24 * 60 * 60 * 1000).toISOString() 
        })
      });

      const transactionsResponse = await fetch(`/api/transactions?${transactionParams}`);
      if (!transactionsResponse.ok) throw new Error('Failed to fetch transactions');
      
      const transactionsData = await transactionsResponse.json();
      if (transactionsData.success) {
        setTransactions(transactionsData.data);
      }

    } catch (err) {
      console.error('Account data fetch error:', err);
      setError(err instanceof Error ? err.message : 'Failed to load account data');
    } finally {
      setLoading(false);
    }
  };

  const calculateSummary = (accountsData: Account[]) => {
    const totalBalance = accountsData.reduce((sum, account) => sum + account.balance, 0);
    const totalAccounts = accountsData.length;
    
    // Calculate monthly income/expenses from recent transactions
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const recentTransactions = transactions.filter(tx => 
      new Date(tx.createdAt) >= thirtyDaysAgo
    );
    
    const monthlyIncome = recentTransactions
      .filter(tx => tx.transactionType === 'credit')
      .reduce((sum, tx) => sum + tx.amount, 0);
    
    const monthlyExpenses = recentTransactions
      .filter(tx => tx.transactionType === 'debit')
      .reduce((sum, tx) => sum + tx.amount, 0);
    
    const savingsRate = monthlyIncome > 0 ? ((monthlyIncome - monthlyExpenses) / monthlyIncome) * 100 : 0;

    setSummary({
      totalBalance,
      totalAccounts,
      monthlyIncome,
      monthlyExpenses,
      savingsRate
    });
  };

  const formatCurrency = (amount: number, currency: string = 'USD'): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  const formatAccountNumber = (accountNumber: string): string => {
    return `****${accountNumber.slice(-4)}`;
  };

  const getAccountTypeIcon = (type: string) => {
    switch (type) {
      case 'checking':
        return <CreditCard className="h-6 w-6" />;
      case 'savings':
        return <TrendingUp className="h-6 w-6" />;
      case 'investment':
        return <PieChart className="h-6 w-6" />;
      default:
        return <CreditCard className="h-6 w-6" />;
    }
  };

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'credit':
        return <ArrowDownLeft className="h-4 w-4 text-green-500" />;
      case 'debit':
        return <ArrowUpRight className="h-4 w-4 text-red-500" />;
      case 'transfer':
        return <ArrowUpRight className="h-4 w-4 text-blue-500" />;
      default:
        return <ArrowUpRight className="h-4 w-4 text-gray-500" />;
    }
  };

  const getUsagePercentage = (used: number, limit: number): number => {
    return limit > 0 ? (used / limit) * 100 : 0;
  };

  const getUsageColor = (percentage: number): string => {
    if (percentage >= 90) return 'bg-red-500';
    if (percentage >= 75) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <CreditCard className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">Access Denied</h3>
          <p className="mt-1 text-sm text-gray-500">Please log in to access your accounts.</p>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Account Dashboard</h1>
              <p className="text-sm text-gray-600">
                Manage your accounts and view transaction history
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <button
                onClick={() => setBalanceVisible(!balanceVisible)}
                className="flex items-center space-x-2 px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                {balanceVisible ? (
                  <>
                    <EyeOff className="h-4 w-4" />
                    <span>Hide Balances</span>
                  </>
                ) : (
                  <>
                    <Eye className="h-4 w-4" />
                    <span>Show Balances</span>
                  </>
                )}
              </button>
              <button className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                <Plus className="h-4 w-4" />
                <span>New Account</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Error Message */}
        {error && (
          <div className="mb-6 rounded-md bg-red-50 p-4">
            <div className="text-sm text-red-700">{error}</div>
          </div>
        )}

        {/* Summary Cards */}
        {summary && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <DollarSign className="h-8 w-8 text-green-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Total Balance</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {balanceVisible ? formatCurrency(summary.totalBalance) : '••••••'}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <CreditCard className="h-8 w-8 text-blue-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Active Accounts</p>
                  <p className="text-2xl font-bold text-gray-900">{summary.totalAccounts}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <TrendingUp className="h-8 w-8 text-purple-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Monthly Income</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {balanceVisible ? formatCurrency(summary.monthlyIncome) : '••••••'}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <BarChart3 className="h-8 w-8 text-orange-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Savings Rate</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {summary.savingsRate.toFixed(1)}%
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Accounts Section */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">Your Accounts</h2>
              </div>
              <div className="p-6">
                {accounts.length === 0 ? (
                  <div className="text-center py-8">
                    <CreditCard className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900">No accounts found</h3>
                    <p className="mt-1 text-sm text-gray-500">Get started by creating your first account.</p>
                  </div>
                ) : (
                  <div className="space-y-6">
                    {accounts.map((account) => (
                      <div
                        key={account.id}
                        className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow"
                      >
                        <div className="flex items-center justify-between mb-4">
                          <div className="flex items-center space-x-3">
                            <div className="flex-shrink-0 text-blue-500">
                              {getAccountTypeIcon(account.accountType)}
                            </div>
                            <div>
                              <h3 className="text-lg font-medium text-gray-900 capitalize">
                                {account.accountType} Account
                              </h3>
                              <p className="text-sm text-gray-500">
                                {formatAccountNumber(account.accountNumber)}
                              </p>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="text-2xl font-bold text-gray-900">
                              {balanceVisible ? formatCurrency(account.balance, account.currency) : '••••••'}
                            </p>
                            <p className="text-sm text-gray-500">
                              Available Balance
                            </p>
                          </div>
                        </div>

                        {/* Usage Limits */}
                        <div className="space-y-3">
                          <div>
                            <div className="flex justify-between text-sm text-gray-600 mb-1">
                              <span>Daily Usage</span>
                              <span>
                                {balanceVisible ? formatCurrency(account.dailyUsage) : '••••'} / {formatCurrency(account.dailyLimit)}
                              </span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div
                                className={`h-2 rounded-full transition-all duration-300 ${getUsageColor(getUsagePercentage(account.dailyUsage, account.dailyLimit))}`}
                                style={{ width: `${Math.min(getUsagePercentage(account.dailyUsage, account.dailyLimit), 100)}%` }}
                              ></div>
                            </div>
                          </div>

                          <div>
                            <div className="flex justify-between text-sm text-gray-600 mb-1">
                              <span>Monthly Usage</span>
                              <span>
                                {balanceVisible ? formatCurrency(account.monthlyUsage) : '••••'} / {formatCurrency(account.monthlyLimit)}
                              </span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div
                                className={`h-2 rounded-full transition-all duration-300 ${getUsageColor(getUsagePercentage(account.monthlyUsage, account.monthlyLimit))}`}
                                style={{ width: `${Math.min(getUsagePercentage(account.monthlyUsage, account.monthlyLimit), 100)}%` }}
                              ></div>
                            </div>
                          </div>
                        </div>

                        {/* Account Actions */}
                        <div className="flex space-x-3 mt-4">
                          <button className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                            View Details
                          </button>
                          <button className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                            Transfer
                          </button>
                          <button className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                            Pay Bills
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Transactions Section */}
          <div>
            <div className="bg-white rounded-lg shadow">
              <div className="px-6 py-4 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h2 className="text-lg font-medium text-gray-900">Recent Transactions</h2>
                  <div className="flex space-x-2">
                    <select
                      value={selectedAccount}
                      onChange={(e) => setSelectedAccount(e.target.value)}
                      className="text-sm border border-gray-300 rounded-md px-2 py-1"
                    >
                      <option value="all">All Accounts</option>
                      {accounts.map((account) => (
                        <option key={account.id} value={account.id}>
                          {account.accountType} ****{account.accountNumber.slice(-4)}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
              </div>
              <div className="p-6">
                {transactions.length === 0 ? (
                  <div className="text-center py-8">
                    <Calendar className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900">No transactions</h3>
                    <p className="mt-1 text-sm text-gray-500">Your recent transactions will appear here.</p>
                  </div>
                ) : (
                  <div className="space-y-4 max-h-96 overflow-y-auto">
                    {transactions.map((transaction) => (
                      <div
                        key={transaction.id}
                        className="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0"
                      >
                        <div className="flex items-center space-x-3">
                          {getTransactionIcon(transaction.transactionType)}
                          <div>
                            <p className="text-sm font-medium text-gray-900">
                              {transaction.description}
                            </p>
                            <p className="text-xs text-gray-500">
                              {transaction.accountType} ****{transaction.accountNumber.slice(-4)}
                            </p>
                            <p className="text-xs text-gray-500">
                              {new Date(transaction.createdAt).toLocaleDateString()}
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className={`text-sm font-medium ${
                            transaction.transactionType === 'credit' 
                              ? 'text-green-600' 
                              : 'text-red-600'
                          }`}>
                            {transaction.transactionType === 'credit' ? '+' : '-'}
                            {balanceVisible ? formatCurrency(transaction.amount) : '••••'}
                          </p>
                          <p className="text-xs text-gray-500">
                            {transaction.status}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="mt-8">
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              <button className="flex items-center justify-center px-4 py-3 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                <ArrowUpRight className="h-4 w-4 mr-2" />
                Transfer Money
              </button>
              <button className="flex items-center justify-center px-4 py-3 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                <CreditCard className="h-4 w-4 mr-2" />
                Pay Bills
              </button>
              <button className="flex items-center justify-center px-4 py-3 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                <Download className="h-4 w-4 mr-2" />
                Export Data
              </button>
              <button className="flex items-center justify-center px-4 py-3 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                <BarChart3 className="h-4 w-4 mr-2" />
                View Reports
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
