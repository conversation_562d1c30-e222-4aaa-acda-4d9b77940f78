'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { sanitizeInput } from '@/utils/security';
import { ArrowRight, Shield, AlertTriangle, CheckCircle, CreditCard } from 'lucide-react';

interface Account {
  id: string;
  accountNumber: string;
  accountType: string;
  balance: number;
  currency: string;
  dailyRemaining: number;
}

interface TransferFormData {
  fromAccountId: string;
  toAccountId: string;
  amount: string;
  description: string;
  transferType: 'internal';
}

interface TransferFormErrors {
  [key: string]: string;
}

export default function TransferForm() {
  const { user, isAuthenticated } = useAuth();
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [formData, setFormData] = useState<TransferFormData>({
    fromAccountId: '',
    toAccountId: '',
    amount: '',
    description: '',
    transferType: 'internal'
  });
  const [errors, setErrors] = useState<TransferFormErrors>({});
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [csrfToken, setCsrfToken] = useState('');
  const [transferSuccess, setTransferSuccess] = useState(false);
  const [transferDetails, setTransferDetails] = useState<any>(null);

  useEffect(() => {
    if (isAuthenticated) {
      generateCSRFToken();
      fetchAccounts();
    }
  }, [isAuthenticated]);

  const generateCSRFToken = () => {
    const token = Array.from(crypto.getRandomValues(new Uint8Array(32)))
      .map(b => b.toString(16).padStart(2, '0'))
      .join('');
    setCsrfToken(token);
  };

  const fetchAccounts = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/accounts', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': csrfToken,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch accounts');
      }

      const data = await response.json();
      if (data.success) {
        setAccounts(data.data);
      }
    } catch (error) {
      console.error('Error fetching accounts:', error);
      setErrors({ general: 'Failed to load accounts. Please refresh the page.' });
    } finally {
      setIsLoading(false);
    }
  };

  const validateForm = (): boolean => {
    const newErrors: TransferFormErrors = {};

    // From account validation
    if (!formData.fromAccountId) {
      newErrors.fromAccountId = 'Please select a source account';
    }

    // To account validation
    if (!formData.toAccountId) {
      newErrors.toAccountId = 'Please select a destination account';
    } else if (formData.fromAccountId === formData.toAccountId) {
      newErrors.toAccountId = 'Source and destination accounts must be different';
    }

    // Amount validation
    if (!formData.amount) {
      newErrors.amount = 'Please enter an amount';
    } else {
      const amount = parseFloat(formData.amount);
      if (isNaN(amount) || amount <= 0) {
        newErrors.amount = 'Please enter a valid amount greater than 0';
      } else if (amount > 1000000) {
        newErrors.amount = 'Amount cannot exceed $1,000,000';
      } else if (!/^\d+(\.\d{1,2})?$/.test(formData.amount)) {
        newErrors.amount = 'Amount can only have up to 2 decimal places';
      } else {
        // Check account balance and limits
        const fromAccount = accounts.find(acc => acc.id === formData.fromAccountId);
        if (fromAccount) {
          if (amount > fromAccount.balance) {
            newErrors.amount = 'Insufficient funds in source account';
          } else if (amount > fromAccount.dailyRemaining) {
            newErrors.amount = `Amount exceeds daily limit. Remaining: $${fromAccount.dailyRemaining.toFixed(2)}`;
          }
        }
      }
    }

    // Description validation
    if (!formData.description.trim()) {
      newErrors.description = 'Please enter a description';
    } else if (formData.description.length > 255) {
      newErrors.description = 'Description cannot exceed 255 characters';
    } else if (!/^[a-zA-Z0-9\s\-_.,!?]+$/.test(formData.description)) {
      newErrors.description = 'Description contains invalid characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    
    // Sanitize input to prevent XSS
    let sanitizedValue = sanitizeInput(value);
    
    // Special handling for amount field
    if (name === 'amount') {
      // Only allow numbers and decimal point
      sanitizedValue = value.replace(/[^0-9.]/g, '');
      // Ensure only one decimal point
      const parts = sanitizedValue.split('.');
      if (parts.length > 2) {
        sanitizedValue = parts[0] + '.' + parts.slice(1).join('');
      }
    }
    
    setFormData(prev => ({
      ...prev,
      [name]: sanitizedValue
    }));

    // Clear specific field error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: undefined
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    setErrors({});

    try {
      const response = await fetch('/api/transfer', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': csrfToken,
        },
        body: JSON.stringify({
          ...formData,
          amount: parseFloat(formData.amount),
          csrfToken
        }),
      });

      const data = await response.json();

      if (data.success) {
        setTransferSuccess(true);
        setTransferDetails(data.data);
        
        // Reset form
        setFormData({
          fromAccountId: '',
          toAccountId: '',
          amount: '',
          description: '',
          transferType: 'internal'
        });
        
        // Refresh accounts to show updated balances
        await fetchAccounts();
      } else {
        setErrors({ general: data.error || 'Transfer failed. Please try again.' });
      }
    } catch (error) {
      console.error('Transfer error:', error);
      setErrors({ general: 'An unexpected error occurred. Please try again.' });
    } finally {
      setIsSubmitting(false);
      generateCSRFToken();
    }
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatAccountDisplay = (account: Account): string => {
    return `${account.accountType.charAt(0).toUpperCase() + account.accountType.slice(1)} - ****${account.accountNumber.slice(-4)} (${formatCurrency(account.balance)})`;
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <Shield className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">Access Denied</h3>
          <p className="mt-1 text-sm text-gray-500">Please log in to access transfers.</p>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md mx-auto">
        <div className="text-center mb-8">
          <div className="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-blue-100">
            <ArrowRight className="h-6 w-6 text-blue-600" />
          </div>
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
            Transfer Money
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            Securely transfer funds between your accounts
          </p>
        </div>

        {/* Success Message */}
        {transferSuccess && transferDetails && (
          <div className="mb-6 rounded-md bg-green-50 p-4">
            <div className="flex">
              <CheckCircle className="h-5 w-5 text-green-400" />
              <div className="ml-3">
                <h3 className="text-sm font-medium text-green-800">
                  Transfer Successful!
                </h3>
                <div className="mt-2 text-sm text-green-700">
                  <p>Amount: {formatCurrency(transferDetails.amount)}</p>
                  <p>Reference: {transferDetails.referenceNumber}</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Error Message */}
        {errors.general && (
          <div className="mb-6 rounded-md bg-red-50 p-4">
            <div className="flex">
              <AlertTriangle className="h-5 w-5 text-red-400" />
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">
                  {errors.general}
                </h3>
              </div>
            </div>
          </div>
        )}

        <div className="bg-white py-8 px-6 shadow rounded-lg">
          <form onSubmit={handleSubmit} className="space-y-6">
            <input type="hidden" name="csrfToken" value={csrfToken} />

            {/* From Account */}
            <div>
              <label htmlFor="fromAccountId" className="block text-sm font-medium text-gray-700">
                From Account
              </label>
              <select
                id="fromAccountId"
                name="fromAccountId"
                required
                disabled={isSubmitting}
                className={`mt-1 block w-full px-3 py-2 border ${
                  errors.fromAccountId ? 'border-red-300' : 'border-gray-300'
                } rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm disabled:bg-gray-100`}
                value={formData.fromAccountId}
                onChange={handleInputChange}
              >
                <option value="">Select source account</option>
                {accounts.map((account) => (
                  <option key={account.id} value={account.id}>
                    {formatAccountDisplay(account)}
                  </option>
                ))}
              </select>
              {errors.fromAccountId && (
                <p className="mt-1 text-sm text-red-600">{errors.fromAccountId}</p>
              )}
            </div>

            {/* To Account */}
            <div>
              <label htmlFor="toAccountId" className="block text-sm font-medium text-gray-700">
                To Account
              </label>
              <select
                id="toAccountId"
                name="toAccountId"
                required
                disabled={isSubmitting}
                className={`mt-1 block w-full px-3 py-2 border ${
                  errors.toAccountId ? 'border-red-300' : 'border-gray-300'
                } rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm disabled:bg-gray-100`}
                value={formData.toAccountId}
                onChange={handleInputChange}
              >
                <option value="">Select destination account</option>
                {accounts
                  .filter(account => account.id !== formData.fromAccountId)
                  .map((account) => (
                    <option key={account.id} value={account.id}>
                      {formatAccountDisplay(account)}
                    </option>
                  ))}
              </select>
              {errors.toAccountId && (
                <p className="mt-1 text-sm text-red-600">{errors.toAccountId}</p>
              )}
            </div>

            {/* Amount */}
            <div>
              <label htmlFor="amount" className="block text-sm font-medium text-gray-700">
                Amount
              </label>
              <div className="mt-1 relative rounded-md shadow-sm">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <span className="text-gray-500 sm:text-sm">$</span>
                </div>
                <input
                  type="text"
                  id="amount"
                  name="amount"
                  required
                  disabled={isSubmitting}
                  className={`block w-full pl-7 pr-3 py-2 border ${
                    errors.amount ? 'border-red-300' : 'border-gray-300'
                  } rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm disabled:bg-gray-100`}
                  placeholder="0.00"
                  value={formData.amount}
                  onChange={handleInputChange}
                  maxLength={10}
                />
              </div>
              {errors.amount && (
                <p className="mt-1 text-sm text-red-600">{errors.amount}</p>
              )}
              
              {/* Show available balance */}
              {formData.fromAccountId && (
                <p className="mt-1 text-sm text-gray-500">
                  Available: {formatCurrency(accounts.find(acc => acc.id === formData.fromAccountId)?.balance || 0)}
                </p>
              )}
            </div>

            {/* Description */}
            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                Description
              </label>
              <textarea
                id="description"
                name="description"
                rows={3}
                required
                disabled={isSubmitting}
                className={`mt-1 block w-full px-3 py-2 border ${
                  errors.description ? 'border-red-300' : 'border-gray-300'
                } rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm disabled:bg-gray-100`}
                placeholder="Enter transfer description..."
                value={formData.description}
                onChange={handleInputChange}
                maxLength={255}
              />
              {errors.description && (
                <p className="mt-1 text-sm text-red-600">{errors.description}</p>
              )}
              <p className="mt-1 text-sm text-gray-500">
                {formData.description.length}/255 characters
              </p>
            </div>

            {/* Transfer Summary */}
            {formData.fromAccountId && formData.toAccountId && formData.amount && (
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="text-sm font-medium text-gray-900 mb-2">Transfer Summary</h3>
                <div className="space-y-1 text-sm text-gray-600">
                  <div className="flex justify-between">
                    <span>From:</span>
                    <span>{accounts.find(acc => acc.id === formData.fromAccountId)?.accountType} ****{accounts.find(acc => acc.id === formData.fromAccountId)?.accountNumber.slice(-4)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>To:</span>
                    <span>{accounts.find(acc => acc.id === formData.toAccountId)?.accountType} ****{accounts.find(acc => acc.id === formData.toAccountId)?.accountNumber.slice(-4)}</span>
                  </div>
                  <div className="flex justify-between font-medium">
                    <span>Amount:</span>
                    <span>{formData.amount ? formatCurrency(parseFloat(formData.amount)) : '$0.00'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Fees:</span>
                    <span>$0.00</span>
                  </div>
                </div>
              </div>
            )}

            {/* Submit Button */}
            <div>
              <button
                type="submit"
                disabled={isSubmitting || accounts.length < 2}
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:bg-gray-400 disabled:cursor-not-allowed"
              >
                {isSubmitting ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Processing Transfer...
                  </div>
                ) : (
                  'Transfer Money'
                )}
              </button>
            </div>

            {accounts.length < 2 && (
              <p className="text-sm text-gray-500 text-center">
                You need at least 2 accounts to make transfers.
              </p>
            )}
          </form>
        </div>
      </div>
    </div>
  );
}
