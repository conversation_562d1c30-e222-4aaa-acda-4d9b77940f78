import { createClient } from '@supabase/supabase-js';
import { createBrowserClient, createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';

// Environment variables validation
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

// Client-side Supabase client
export const createClientComponentClient = () => {
  return createBrowserClient(supabaseUrl, supabaseAnonKey);
};

// Server-side Supabase client for Server Components
export const createServerComponentClient = async () => {
  const cookieStore = await cookies();
  
  return createServerClient(supabaseUrl, supabaseAnonKey, {
    cookies: {
      getAll() {
        return cookieStore.getAll();
      },
      setAll(cookiesToSet) {
        try {
          cookiesToSet.forEach(({ name, value, options }) =>
            cookieStore.set(name, value, options)
          );
        } catch {
          // The `setAll` method was called from a Server Component.
          // This can be ignored if you have middleware refreshing
          // user sessions.
        }
      },
    },
  });
};

// Server-side Supabase client for Route Handlers
export const createRouteHandlerClient = (
  request: NextRequest,
  response: NextResponse
) => {
  return createServerClient(supabaseUrl, supabaseAnonKey, {
    cookies: {
      getAll() {
        return request.cookies.getAll();
      },
      setAll(cookiesToSet) {
        cookiesToSet.forEach(({ name, value }) => request.cookies.set(name, value));
        cookiesToSet.forEach(({ name, value, options }) =>
          response.cookies.set(name, value, options)
        );
      },
    },
  });
};

// Admin client with service role key (use with caution)
export const createAdminClient = () => {
  if (!supabaseServiceKey) {
    throw new Error('Missing Supabase service role key');
  }
  
  return createClient(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  });
};

// Database types
export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string;
          email: string;
          created_at: string;
          updated_at: string;
          role: 'user' | 'admin' | 'manager';
          is_active: boolean;
          last_login: string | null;
          failed_login_attempts: number;
          locked_until: string | null;
        };
        Insert: {
          id?: string;
          email: string;
          role?: 'user' | 'admin' | 'manager';
          is_active?: boolean;
          last_login?: string | null;
          failed_login_attempts?: number;
          locked_until?: string | null;
        };
        Update: {
          id?: string;
          email?: string;
          role?: 'user' | 'admin' | 'manager';
          is_active?: boolean;
          last_login?: string | null;
          failed_login_attempts?: number;
          locked_until?: string | null;
        };
      };
      accounts: {
        Row: {
          id: string;
          user_id: string;
          account_number: string;
          account_type: 'checking' | 'savings' | 'investment';
          balance: number;
          currency: string;
          is_active: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          account_number: string;
          account_type: 'checking' | 'savings' | 'investment';
          balance?: number;
          currency?: string;
          is_active?: boolean;
        };
        Update: {
          id?: string;
          user_id?: string;
          account_number?: string;
          account_type?: 'checking' | 'savings' | 'investment';
          balance?: number;
          currency?: string;
          is_active?: boolean;
        };
      };
      transactions: {
        Row: {
          id: string;
          account_id: string;
          transaction_type: 'debit' | 'credit' | 'transfer';
          amount: number;
          description: string;
          reference_number: string;
          status: 'pending' | 'completed' | 'failed' | 'cancelled';
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          account_id: string;
          transaction_type: 'debit' | 'credit' | 'transfer';
          amount: number;
          description: string;
          reference_number: string;
          status?: 'pending' | 'completed' | 'failed' | 'cancelled';
        };
        Update: {
          id?: string;
          account_id?: string;
          transaction_type?: 'debit' | 'credit' | 'transfer';
          amount?: number;
          description?: string;
          reference_number?: string;
          status?: 'pending' | 'completed' | 'failed' | 'cancelled';
        };
      };
      audit_logs: {
        Row: {
          id: string;
          user_id: string | null;
          action: string;
          resource: string;
          resource_id: string | null;
          ip_address: string;
          user_agent: string;
          metadata: Record<string, any> | null;
          created_at: string;
        };
        Insert: {
          id?: string;
          user_id?: string | null;
          action: string;
          resource: string;
          resource_id?: string | null;
          ip_address: string;
          user_agent: string;
          metadata?: Record<string, any> | null;
        };
        Update: {
          id?: string;
          user_id?: string | null;
          action?: string;
          resource?: string;
          resource_id?: string | null;
          ip_address?: string;
          user_agent?: string;
          metadata?: Record<string, any> | null;
        };
      };
    };
  };
}
