import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { createRouteHandlerClient } from '@/lib/supabase';
import { verifyAccessToken, getClientIP, getUserAgent } from '@/utils/security';

// Rate limiting store (in production, use Redis or similar)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

// Security headers
const securityHeaders = {
  'X-Frame-Options': 'DENY',
  'X-Content-Type-Options': 'nosniff',
  'X-XSS-Protection': '1; mode=block',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'Permissions-Policy': 'camera=(), microphone=(), geolocation=(), payment=()',
  'Strict-Transport-Security': 'max-age=********; includeSubDomains; preload',
  'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self'; connect-src 'self' https://*.supabase.co; frame-ancestors 'none';",
};

// Protected routes that require authentication
const protectedRoutes = [
  '/dashboard',
  '/transactions',
  '/profile',
  '/api/user',
  '/api/accounts',
  '/api/transactions',
  '/api/transfer',
  '/api/bills',
];

// Admin routes that require admin role
const adminRoutes = [
  '/admin',
  '/api/admin',
];

// Public routes that don't require authentication
const publicRoutes = [
  '/',
  '/auth/login',
  '/auth/register',
  '/auth/forgot-password',
  '/api/auth',
];

// Rate limiting configuration
const rateLimitConfig = {
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxRequests: 100, // limit each IP to 100 requests per windowMs
  authWindowMs: 15 * 60 * 1000, // 15 minutes for auth endpoints
  authMaxRequests: 5, // limit auth attempts
};

function isRateLimited(key: string, maxRequests: number, windowMs: number): boolean {
  const now = Date.now();
  const record = rateLimitStore.get(key);

  if (!record || now > record.resetTime) {
    rateLimitStore.set(key, { count: 1, resetTime: now + windowMs });
    return false;
  }

  if (record.count >= maxRequests) {
    return true;
  }

  record.count++;
  return false;
}

function isProtectedRoute(pathname: string): boolean {
  return protectedRoutes.some(route => pathname.startsWith(route));
}

function isAdminRoute(pathname: string): boolean {
  return adminRoutes.some(route => pathname.startsWith(route));
}

function isPublicRoute(pathname: string): boolean {
  return publicRoutes.some(route => pathname === route || pathname.startsWith(route));
}

function isAuthRoute(pathname: string): boolean {
  return pathname.startsWith('/api/auth') || pathname.startsWith('/auth');
}

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  const response = NextResponse.next();
  
  // Add security headers to all responses
  Object.entries(securityHeaders).forEach(([key, value]) => {
    response.headers.set(key, value);
  });

  // Get client information for logging and rate limiting
  const clientIP = getClientIP(request);
  const userAgent = getUserAgent(request);
  
  // Rate limiting
  const rateLimitKey = `rate_limit:${clientIP}`;
  const authRateLimitKey = `auth_rate_limit:${clientIP}`;
  
  // Apply stricter rate limiting for auth routes
  if (isAuthRoute(pathname)) {
    if (isRateLimited(authRateLimitKey, rateLimitConfig.authMaxRequests, rateLimitConfig.authWindowMs)) {
      console.warn(`Rate limit exceeded for auth route: ${pathname} from IP: ${clientIP}`);
      return new NextResponse('Too Many Requests', { 
        status: 429,
        headers: {
          'Retry-After': '900', // 15 minutes
          ...securityHeaders,
        },
      });
    }
  } else {
    // General rate limiting
    if (isRateLimited(rateLimitKey, rateLimitConfig.maxRequests, rateLimitConfig.windowMs)) {
      console.warn(`Rate limit exceeded: ${pathname} from IP: ${clientIP}`);
      return new NextResponse('Too Many Requests', { 
        status: 429,
        headers: {
          'Retry-After': '900', // 15 minutes
          ...securityHeaders,
        },
      });
    }
  }

  // Skip authentication for public routes
  if (isPublicRoute(pathname)) {
    return response;
  }

  // Authentication check for protected routes
  if (isProtectedRoute(pathname) || isAdminRoute(pathname)) {
    try {
      // Create Supabase client
      const supabase = createRouteHandlerClient(request, response);
      
      // Get session from Supabase
      const { data: { session }, error } = await supabase.auth.getSession();
      
      if (error || !session) {
        console.warn(`Unauthorized access attempt: ${pathname} from IP: ${clientIP}`);
        
        // Redirect to login for page routes
        if (!pathname.startsWith('/api')) {
          const loginUrl = new URL('/auth/login', request.url);
          loginUrl.searchParams.set('redirect', pathname);
          return NextResponse.redirect(loginUrl);
        }
        
        // Return 401 for API routes
        return new NextResponse('Unauthorized', { 
          status: 401,
          headers: securityHeaders,
        });
      }

      // Additional JWT verification for API routes
      if (pathname.startsWith('/api')) {
        const authHeader = request.headers.get('authorization');
        if (authHeader && authHeader.startsWith('Bearer ')) {
          const token = authHeader.substring(7);
          const payload = verifyAccessToken(token);
          
          if (!payload) {
            console.warn(`Invalid JWT token: ${pathname} from IP: ${clientIP}`);
            return new NextResponse('Invalid Token', { 
              status: 401,
              headers: securityHeaders,
            });
          }
          
          // Add user info to headers for API routes
          response.headers.set('X-User-ID', payload.userId);
          response.headers.set('X-User-Role', payload.role);
        }
      }

      // Admin role check for admin routes
      if (isAdminRoute(pathname)) {
        const { data: user } = await supabase
          .from('users')
          .select('role')
          .eq('id', session.user.id)
          .single();
        
        if (!user || (user.role !== 'admin' && user.role !== 'manager')) {
          console.warn(`Insufficient permissions: ${pathname} from user: ${session.user.id}, IP: ${clientIP}`);
          
          if (!pathname.startsWith('/api')) {
            return NextResponse.redirect(new URL('/dashboard', request.url));
          }
          
          return new NextResponse('Forbidden', { 
            status: 403,
            headers: securityHeaders,
          });
        }
      }

      // Log successful access for audit purposes
      if (process.env.ENABLE_AUDIT_LOGGING === 'true') {
        // In production, this should be async and non-blocking
        console.log(`Access granted: ${pathname} for user: ${session.user.id}, IP: ${clientIP}, UA: ${userAgent}`);
      }

    } catch (error) {
      console.error('Middleware authentication error:', error);
      
      if (!pathname.startsWith('/api')) {
        return NextResponse.redirect(new URL('/auth/login', request.url));
      }
      
      return new NextResponse('Internal Server Error', { 
        status: 500,
        headers: securityHeaders,
      });
    }
  }

  return response;
}

// Configure which routes the middleware should run on
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    '/((?!_next/static|_next/image|favicon.ico|public/).*)',
  ],
};
