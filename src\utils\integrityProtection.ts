// Software and Data Integrity Protection
// Implements OWASP A08: Software and Data Integrity Failures prevention

import crypto from 'crypto';
import { NextRequest } from 'next/server';

// Known good hashes for critical files (in production, load from secure config)
const CRITICAL_FILE_HASHES = {
  '/api/auth/login': 'sha256-abc123...', // Example hash
  '/api/transfer': 'sha256-def456...',   // Example hash
  // Add hashes for other critical endpoints
};

// Trusted package sources and their public keys
const TRUSTED_SOURCES = {
  npm: {
    registry: 'https://registry.npmjs.org',
    publicKey: '-----BEGIN PUBLIC KEY-----\n...\n-----END PUBLIC KEY-----'
  },
  github: {
    domain: 'github.com',
    publicKey: '-----BEGIN PUBLIC KEY-----\n...\n-----<PERSON><PERSON> PUBLIC KEY-----'
  }
};

interface IntegrityCheckResult {
  valid: boolean;
  reason?: string;
  hash?: string;
}

interface SignatureVerificationResult {
  valid: boolean;
  signer?: string;
  timestamp?: Date;
  reason?: string;
}

// Generate SHA-256 hash for content
export function generateHash(content: string | Buffer): string {
  return crypto.createHash('sha256').update(content).digest('hex');
}

// Generate SHA-384 hash (more secure for critical operations)
export function generateSecureHash(content: string | Buffer): string {
  return crypto.createHash('sha384').update(content).digest('base64');
}

// Verify content integrity using hash
export function verifyContentIntegrity(
  content: string | Buffer, 
  expectedHash: string,
  algorithm: 'sha256' | 'sha384' = 'sha256'
): IntegrityCheckResult {
  try {
    const actualHash = algorithm === 'sha256' 
      ? generateHash(content)
      : generateSecureHash(content);
    
    const valid = actualHash === expectedHash;
    
    return {
      valid,
      hash: actualHash,
      reason: valid ? undefined : 'Hash mismatch detected'
    };
  } catch (error) {
    return {
      valid: false,
      reason: 'Failed to compute hash'
    };
  }
}

// Generate HMAC for message authentication
export function generateHMAC(
  message: string, 
  secret: string, 
  algorithm: string = 'sha256'
): string {
  return crypto.createHmac(algorithm, secret).update(message).digest('hex');
}

// Verify HMAC signature
export function verifyHMAC(
  message: string, 
  signature: string, 
  secret: string,
  algorithm: string = 'sha256'
): boolean {
  try {
    const expectedSignature = generateHMAC(message, secret, algorithm);
    return crypto.timingSafeEqual(
      Buffer.from(signature, 'hex'),
      Buffer.from(expectedSignature, 'hex')
    );
  } catch {
    return false;
  }
}

// Digital signature verification (for external data)
export function verifyDigitalSignature(
  data: string,
  signature: string,
  publicKey: string,
  algorithm: string = 'RSA-SHA256'
): SignatureVerificationResult {
  try {
    const verifier = crypto.createVerify(algorithm);
    verifier.update(data);
    
    const isValid = verifier.verify(publicKey, signature, 'base64');
    
    return {
      valid: isValid,
      reason: isValid ? undefined : 'Invalid digital signature'
    };
  } catch (error) {
    return {
      valid: false,
      reason: 'Signature verification failed'
    };
  }
}

// Subresource Integrity (SRI) hash generator
export function generateSRIHash(content: string): string {
  const hash = crypto.createHash('sha384').update(content).digest('base64');
  return `sha384-${hash}`;
}

// Verify SRI hash from HTML attributes
export function verifySRIHash(content: string, sriHash: string): boolean {
  const expectedHash = generateSRIHash(content);
  return expectedHash === sriHash;
}

// Package integrity checker for dependencies
export interface PackageInfo {
  name: string;
  version: string;
  integrity?: string;
  resolved?: string;
  tarball?: string;
}

export function verifyPackageIntegrity(
  packageContent: Buffer,
  packageInfo: PackageInfo
): IntegrityCheckResult {
  if (!packageInfo.integrity) {
    return {
      valid: false,
      reason: 'No integrity hash provided'
    };
  }
  
  try {
    // Parse integrity string (format: algorithm-hash)
    const [algorithm, expectedHash] = packageInfo.integrity.split('-');
    
    let actualHash: string;
    switch (algorithm) {
      case 'sha1':
        actualHash = crypto.createHash('sha1').update(packageContent).digest('base64');
        break;
      case 'sha256':
        actualHash = crypto.createHash('sha256').update(packageContent).digest('base64');
        break;
      case 'sha384':
        actualHash = crypto.createHash('sha384').update(packageContent).digest('base64');
        break;
      case 'sha512':
        actualHash = crypto.createHash('sha512').update(packageContent).digest('base64');
        break;
      default:
        return {
          valid: false,
          reason: `Unsupported hash algorithm: ${algorithm}`
        };
    }
    
    const valid = actualHash === expectedHash;
    
    return {
      valid,
      hash: `${algorithm}-${actualHash}`,
      reason: valid ? undefined : 'Package integrity check failed'
    };
  } catch (error) {
    return {
      valid: false,
      reason: 'Failed to verify package integrity'
    };
  }
}

// Webhook signature verification (for external services)
export function verifyWebhookSignature(
  payload: string,
  signature: string,
  secret: string,
  algorithm: 'sha1' | 'sha256' = 'sha256'
): boolean {
  try {
    const expectedSignature = crypto
      .createHmac(algorithm, secret)
      .update(payload)
      .digest('hex');
    
    // Handle different signature formats
    const cleanSignature = signature.replace(/^(sha1|sha256)=/, '');
    
    return crypto.timingSafeEqual(
      Buffer.from(cleanSignature, 'hex'),
      Buffer.from(expectedSignature, 'hex')
    );
  } catch {
    return false;
  }
}

// Content Security Policy (CSP) nonce generator
export function generateCSPNonce(): string {
  return crypto.randomBytes(16).toString('base64');
}

// Secure random token generator for various purposes
export function generateSecureToken(length: number = 32): string {
  return crypto.randomBytes(length).toString('hex');
}

// File upload integrity checker
export function verifyUploadedFile(
  fileBuffer: Buffer,
  expectedHash?: string,
  maxSize: number = 10 * 1024 * 1024 // 10MB default
): IntegrityCheckResult {
  // Check file size
  if (fileBuffer.length > maxSize) {
    return {
      valid: false,
      reason: 'File size exceeds maximum allowed'
    };
  }
  
  // Generate hash
  const fileHash = generateHash(fileBuffer);
  
  // If expected hash provided, verify it
  if (expectedHash) {
    const valid = fileHash === expectedHash;
    return {
      valid,
      hash: fileHash,
      reason: valid ? undefined : 'File integrity check failed'
    };
  }
  
  return {
    valid: true,
    hash: fileHash
  };
}

// API request integrity middleware
export function createIntegrityMiddleware(secret: string) {
  return (req: NextRequest) => {
    const signature = req.headers.get('x-signature');
    const timestamp = req.headers.get('x-timestamp');
    
    if (!signature || !timestamp) {
      return {
        valid: false,
        reason: 'Missing signature or timestamp'
      };
    }
    
    // Check timestamp to prevent replay attacks (5 minute window)
    const requestTime = parseInt(timestamp);
    const currentTime = Math.floor(Date.now() / 1000);
    
    if (Math.abs(currentTime - requestTime) > 300) {
      return {
        valid: false,
        reason: 'Request timestamp outside acceptable window'
      };
    }
    
    // Verify signature
    const payload = `${timestamp}.${req.body}`;
    const isValid = verifyHMAC(payload, signature, secret);
    
    return {
      valid: isValid,
      reason: isValid ? undefined : 'Invalid request signature'
    };
  };
}

// Configuration integrity checker
export function verifyConfigIntegrity(
  config: Record<string, any>,
  expectedHash?: string
): IntegrityCheckResult {
  try {
    // Normalize config object for consistent hashing
    const normalizedConfig = JSON.stringify(config, Object.keys(config).sort());
    const configHash = generateHash(normalizedConfig);
    
    if (expectedHash) {
      const valid = configHash === expectedHash;
      return {
        valid,
        hash: configHash,
        reason: valid ? undefined : 'Configuration integrity check failed'
      };
    }
    
    return {
      valid: true,
      hash: configHash
    };
  } catch (error) {
    return {
      valid: false,
      reason: 'Failed to verify configuration integrity'
    };
  }
}

// Database integrity checker (for critical data)
export function generateDataIntegrityHash(data: Record<string, any>): string {
  // Remove timestamps and other volatile fields
  const stableData = { ...data };
  delete stableData.updated_at;
  delete stableData.last_accessed;
  
  // Sort keys for consistent hashing
  const sortedData = Object.keys(stableData)
    .sort()
    .reduce((result, key) => {
      result[key] = stableData[key];
      return result;
    }, {} as Record<string, any>);
  
  return generateHash(JSON.stringify(sortedData));
}

// Audit log integrity protection
export interface AuditLogEntry {
  id: string;
  timestamp: string;
  action: string;
  userId?: string;
  data: Record<string, any>;
  previousHash?: string;
  hash?: string;
}

export function generateAuditLogHash(
  entry: Omit<AuditLogEntry, 'hash'>,
  previousHash: string = ''
): string {
  const hashInput = `${entry.id}|${entry.timestamp}|${entry.action}|${entry.userId || ''}|${JSON.stringify(entry.data)}|${previousHash}`;
  return generateHash(hashInput);
}

export function verifyAuditLogChain(logs: AuditLogEntry[]): boolean {
  if (logs.length === 0) return true;
  
  let previousHash = '';
  
  for (const log of logs) {
    const expectedHash = generateAuditLogHash(log, previousHash);
    
    if (log.hash !== expectedHash) {
      return false;
    }
    
    previousHash = log.hash;
  }
  
  return true;
}

export default {
  generateHash,
  generateSecureHash,
  verifyContentIntegrity,
  generateHMAC,
  verifyHMAC,
  verifyDigitalSignature,
  generateSRIHash,
  verifySRIHash,
  verifyPackageIntegrity,
  verifyWebhookSignature,
  generateCSPNonce,
  generateSecureToken,
  verifyUploadedFile,
  createIntegrityMiddleware,
  verifyConfigIntegrity,
  generateDataIntegrityHash,
  generateAuditLogHash,
  verifyAuditLogChain
};
