import { NextRequest } from 'next/server';
import { getClientIP } from './security';

// In-memory store for rate limiting (in production, use Redis)
interface RateLimitEntry {
  count: number;
  resetTime: number;
  blocked: boolean;
  blockUntil?: number;
}

class RateLimiter {
  private store = new Map<string, RateLimitEntry>();
  private cleanupInterval: NodeJS.Timeout;

  constructor() {
    // Clean up expired entries every 5 minutes
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 5 * 60 * 1000);
  }

  private cleanup() {
    const now = Date.now();
    for (const [key, entry] of this.store.entries()) {
      if (entry.resetTime < now && (!entry.blocked || (entry.blockUntil && entry.blockUntil < now))) {
        this.store.delete(key);
      }
    }
  }

  private getKey(identifier: string, endpoint?: string): string {
    return endpoint ? `${identifier}:${endpoint}` : identifier;
  }

  public checkLimit(
    identifier: string,
    maxRequests: number,
    windowMs: number,
    endpoint?: string
  ): { allowed: boolean; remaining: number; resetTime: number; retryAfter?: number } {
    const key = this.getKey(identifier, endpoint);
    const now = Date.now();
    const entry = this.store.get(key);

    // Check if currently blocked
    if (entry?.blocked && entry.blockUntil && entry.blockUntil > now) {
      return {
        allowed: false,
        remaining: 0,
        resetTime: entry.resetTime,
        retryAfter: Math.ceil((entry.blockUntil - now) / 1000)
      };
    }

    // Reset window if expired
    if (!entry || entry.resetTime <= now) {
      this.store.set(key, {
        count: 1,
        resetTime: now + windowMs,
        blocked: false
      });
      return {
        allowed: true,
        remaining: maxRequests - 1,
        resetTime: now + windowMs
      };
    }

    // Increment count
    entry.count++;

    // Check if limit exceeded
    if (entry.count > maxRequests) {
      // Block for additional time on repeated violations
      const blockDuration = this.calculateBlockDuration(entry.count - maxRequests);
      entry.blocked = true;
      entry.blockUntil = now + blockDuration;

      return {
        allowed: false,
        remaining: 0,
        resetTime: entry.resetTime,
        retryAfter: Math.ceil(blockDuration / 1000)
      };
    }

    return {
      allowed: true,
      remaining: maxRequests - entry.count,
      resetTime: entry.resetTime
    };
  }

  private calculateBlockDuration(violations: number): number {
    // Exponential backoff: 1min, 5min, 15min, 1hr, 24hr
    const baseDuration = 60 * 1000; // 1 minute
    const multipliers = [1, 5, 15, 60, 1440]; // minutes
    const multiplier = multipliers[Math.min(violations - 1, multipliers.length - 1)];
    return baseDuration * multiplier;
  }

  public reset(identifier: string, endpoint?: string): void {
    const key = this.getKey(identifier, endpoint);
    this.store.delete(key);
  }

  public getStats(): { totalKeys: number; blockedKeys: number } {
    const now = Date.now();
    let blockedKeys = 0;
    
    for (const entry of this.store.values()) {
      if (entry.blocked && entry.blockUntil && entry.blockUntil > now) {
        blockedKeys++;
      }
    }

    return {
      totalKeys: this.store.size,
      blockedKeys
    };
  }

  public destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.store.clear();
  }
}

// Global rate limiter instance
const globalRateLimiter = new RateLimiter();

// Rate limiting configurations for different endpoints
export const RATE_LIMITS = {
  // Authentication endpoints
  auth: {
    login: { maxRequests: 5, windowMs: 15 * 60 * 1000 }, // 5 attempts per 15 minutes
    register: { maxRequests: 3, windowMs: 60 * 60 * 1000 }, // 3 attempts per hour
    refresh: { maxRequests: 10, windowMs: 15 * 60 * 1000 }, // 10 refreshes per 15 minutes
  },
  
  // API endpoints
  api: {
    general: { maxRequests: 100, windowMs: 15 * 60 * 1000 }, // 100 requests per 15 minutes
    transactions: { maxRequests: 50, windowMs: 15 * 60 * 1000 }, // 50 requests per 15 minutes
    transfer: { maxRequests: 10, windowMs: 15 * 60 * 1000 }, // 10 transfers per 15 minutes
    profile: { maxRequests: 20, windowMs: 15 * 60 * 1000 }, // 20 profile updates per 15 minutes
  },
  
  // Admin endpoints
  admin: {
    general: { maxRequests: 200, windowMs: 15 * 60 * 1000 }, // 200 requests per 15 minutes
  }
};

// Rate limiting middleware function
export function rateLimit(
  request: NextRequest,
  config: { maxRequests: number; windowMs: number },
  endpoint?: string
): { allowed: boolean; remaining: number; resetTime: number; retryAfter?: number } {
  const clientIP = getClientIP(request);
  const userAgent = request.headers.get('user-agent') || 'unknown';
  
  // Create identifier combining IP and user agent hash for better uniqueness
  const identifier = `${clientIP}:${hashString(userAgent)}`;
  
  return globalRateLimiter.checkLimit(
    identifier,
    config.maxRequests,
    config.windowMs,
    endpoint
  );
}

// User-specific rate limiting (requires authentication)
export function userRateLimit(
  userId: string,
  config: { maxRequests: number; windowMs: number },
  endpoint?: string
): { allowed: boolean; remaining: number; resetTime: number; retryAfter?: number } {
  return globalRateLimiter.checkLimit(
    `user:${userId}`,
    config.maxRequests,
    config.windowMs,
    endpoint
  );
}

// Reset rate limit for a specific identifier
export function resetRateLimit(request: NextRequest, endpoint?: string): void {
  const clientIP = getClientIP(request);
  const userAgent = request.headers.get('user-agent') || 'unknown';
  const identifier = `${clientIP}:${hashString(userAgent)}`;
  
  globalRateLimiter.reset(identifier, endpoint);
}

// Reset rate limit for a specific user
export function resetUserRateLimit(userId: string, endpoint?: string): void {
  globalRateLimiter.reset(`user:${userId}`, endpoint);
}

// Get rate limiter statistics
export function getRateLimiterStats(): { totalKeys: number; blockedKeys: number } {
  return globalRateLimiter.getStats();
}

// Helper function to hash strings
function hashString(str: string): string {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return Math.abs(hash).toString(36);
}

// Rate limiting decorator for API routes
export function withRateLimit(
  config: { maxRequests: number; windowMs: number },
  endpoint?: string
) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (request: NextRequest, ...args: any[]) {
      const result = rateLimit(request, config, endpoint);
      
      if (!result.allowed) {
        const response = new Response(
          JSON.stringify({
            success: false,
            error: 'Rate limit exceeded',
            retryAfter: result.retryAfter
          }),
          {
            status: 429,
            headers: {
              'Content-Type': 'application/json',
              'X-RateLimit-Limit': config.maxRequests.toString(),
              'X-RateLimit-Remaining': result.remaining.toString(),
              'X-RateLimit-Reset': new Date(result.resetTime).toISOString(),
              'Retry-After': result.retryAfter?.toString() || '60'
            }
          }
        );
        return response;
      }

      // Add rate limit headers to successful responses
      const response = await method.apply(this, [request, ...args]);
      
      if (response instanceof Response) {
        response.headers.set('X-RateLimit-Limit', config.maxRequests.toString());
        response.headers.set('X-RateLimit-Remaining', result.remaining.toString());
        response.headers.set('X-RateLimit-Reset', new Date(result.resetTime).toISOString());
      }

      return response;
    };

    return descriptor;
  };
}

// Cleanup function for graceful shutdown
export function cleanupRateLimiter(): void {
  globalRateLimiter.destroy();
}

// Advanced rate limiting with different tiers
export class TieredRateLimiter {
  private rateLimiter: RateLimiter;

  constructor() {
    this.rateLimiter = new RateLimiter();
  }

  public checkTieredLimit(
    identifier: string,
    userTier: 'basic' | 'premium' | 'enterprise',
    endpoint: string
  ): { allowed: boolean; remaining: number; resetTime: number; retryAfter?: number } {
    const tierLimits = {
      basic: { maxRequests: 100, windowMs: 15 * 60 * 1000 },
      premium: { maxRequests: 500, windowMs: 15 * 60 * 1000 },
      enterprise: { maxRequests: 2000, windowMs: 15 * 60 * 1000 }
    };

    const config = tierLimits[userTier];
    return this.rateLimiter.checkLimit(identifier, config.maxRequests, config.windowMs, endpoint);
  }
}

export default globalRateLimiter;
