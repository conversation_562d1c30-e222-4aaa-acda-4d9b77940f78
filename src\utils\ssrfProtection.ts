// Server-Side Request Forgery (SSRF) Protection
// Implements OWASP A10: Server-Side Request Forgery prevention

import { URL } from 'url';
import dns from 'dns';
import { promisify } from 'util';

const dnsLookup = promisify(dns.lookup);

// Allowed domains for external requests
const ALLOWED_DOMAINS = [
  'api.stripe.com',
  'api.plaid.com',
  'api.twilio.com',
  'hooks.slack.com',
  // Add other trusted external services
];

// Allowed IP ranges (CIDR notation)
const ALLOWED_IP_RANGES = [
  // Public APIs that we trust
  '**********/12', // Cloudflare
  '**********/13', // Cloudflare
  // Add other trusted IP ranges
];

// Blocked private IP ranges (RFC 1918, RFC 3927, etc.)
const BLOCKED_IP_RANGES = [
  '10.0.0.0/8',        // Private network
  '**********/12',     // Private network
  '***********/16',    // Private network
  '*********/8',       // Loopback
  '***********/16',    // Link-local
  '*********/4',       // Multicast
  '240.0.0.0/4',       // Reserved
  '0.0.0.0/8',         // Current network
  '**********/10',     // Shared address space
  '**********/15',     // Benchmarking
  '***********/24',    // Documentation
  '::1/128',           // IPv6 loopback
  'fc00::/7',          // IPv6 private
  'fe80::/10',         // IPv6 link-local
];

interface SSRFValidationResult {
  allowed: boolean;
  reason?: string;
  resolvedIP?: string;
}

// Convert CIDR to IP range check
function isIPInRange(ip: string, cidr: string): boolean {
  const [network, prefixLength] = cidr.split('/');
  const prefix = parseInt(prefixLength, 10);
  
  // Convert IP addresses to 32-bit integers for IPv4
  const ipToInt = (ipStr: string): number => {
    return ipStr.split('.').reduce((acc, octet) => (acc << 8) + parseInt(octet, 10), 0) >>> 0;
  };
  
  const networkInt = ipToInt(network);
  const ipInt = ipToInt(ip);
  const mask = (0xffffffff << (32 - prefix)) >>> 0;
  
  return (networkInt & mask) === (ipInt & mask);
}

// Check if IP is in blocked ranges
function isBlockedIP(ip: string): boolean {
  return BLOCKED_IP_RANGES.some(range => isIPInRange(ip, range));
}

// Check if IP is in allowed ranges
function isAllowedIP(ip: string): boolean {
  if (ALLOWED_IP_RANGES.length === 0) return true;
  return ALLOWED_IP_RANGES.some(range => isIPInRange(ip, range));
}

// Validate URL for SSRF protection
export async function validateURL(urlString: string): Promise<SSRFValidationResult> {
  try {
    // Parse URL
    const url = new URL(urlString);
    
    // Check protocol
    if (!['http:', 'https:'].includes(url.protocol)) {
      return {
        allowed: false,
        reason: 'Only HTTP and HTTPS protocols are allowed'
      };
    }
    
    // Check for localhost and private IPs in hostname
    const hostname = url.hostname.toLowerCase();
    
    // Block localhost variations
    const localhostPatterns = [
      'localhost',
      '127.0.0.1',
      '0.0.0.0',
      '::1',
      '0:0:0:0:0:0:0:1'
    ];
    
    if (localhostPatterns.includes(hostname)) {
      return {
        allowed: false,
        reason: 'Localhost access is not allowed'
      };
    }
    
    // Check if domain is in allowed list
    const isAllowedDomain = ALLOWED_DOMAINS.some(domain => 
      hostname === domain || hostname.endsWith('.' + domain)
    );
    
    if (ALLOWED_DOMAINS.length > 0 && !isAllowedDomain) {
      return {
        allowed: false,
        reason: 'Domain is not in the allowed list'
      };
    }
    
    // Resolve hostname to IP
    let resolvedIP: string;
    try {
      const result = await dnsLookup(hostname);
      resolvedIP = result.address;
    } catch (error) {
      return {
        allowed: false,
        reason: 'Failed to resolve hostname'
      };
    }
    
    // Check if resolved IP is blocked
    if (isBlockedIP(resolvedIP)) {
      return {
        allowed: false,
        reason: 'Resolved IP is in blocked range',
        resolvedIP
      };
    }
    
    // Check if resolved IP is allowed (if allowlist is configured)
    if (!isAllowedIP(resolvedIP)) {
      return {
        allowed: false,
        reason: 'Resolved IP is not in allowed range',
        resolvedIP
      };
    }
    
    return {
      allowed: true,
      resolvedIP
    };
    
  } catch (error) {
    return {
      allowed: false,
      reason: 'Invalid URL format'
    };
  }
}

// Safe HTTP client wrapper
export async function safeFetch(
  url: string, 
  options: RequestInit = {},
  timeout: number = 10000
): Promise<Response> {
  // Validate URL first
  const validation = await validateURL(url);
  
  if (!validation.allowed) {
    throw new Error(`SSRF Protection: ${validation.reason}`);
  }
  
  // Add timeout to prevent hanging requests
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);
  
  try {
    const response = await fetch(url, {
      ...options,
      signal: controller.signal,
      // Add security headers
      headers: {
        'User-Agent': 'SecureFinancialPortal/1.0',
        ...options.headers
      }
    });
    
    clearTimeout(timeoutId);
    return response;
    
  } catch (error) {
    clearTimeout(timeoutId);
    throw error;
  }
}

// Webhook URL validator for external integrations
export function validateWebhookURL(url: string): boolean {
  try {
    const parsedURL = new URL(url);
    
    // Must be HTTPS for webhooks
    if (parsedURL.protocol !== 'https:') {
      return false;
    }
    
    // Check against allowed domains
    const hostname = parsedURL.hostname.toLowerCase();
    return ALLOWED_DOMAINS.some(domain => 
      hostname === domain || hostname.endsWith('.' + domain)
    );
    
  } catch {
    return false;
  }
}

// URL sanitizer for user inputs
export function sanitizeURL(url: string): string {
  try {
    const parsedURL = new URL(url);
    
    // Remove dangerous parameters
    const dangerousParams = ['javascript', 'data', 'vbscript', 'file'];
    dangerousParams.forEach(param => {
      parsedURL.searchParams.delete(param);
    });
    
    // Ensure safe protocol
    if (!['http:', 'https:'].includes(parsedURL.protocol)) {
      parsedURL.protocol = 'https:';
    }
    
    return parsedURL.toString();
    
  } catch {
    // Return empty string for invalid URLs
    return '';
  }
}

// Configuration for different environments
export const SSRFConfig = {
  development: {
    allowLocalhost: false, // Even in dev, be strict
    allowPrivateIPs: false,
    logBlocked: true
  },
  production: {
    allowLocalhost: false,
    allowPrivateIPs: false,
    logBlocked: true
  }
};

// Logging function for blocked requests
export function logSSRFAttempt(
  url: string, 
  reason: string, 
  userAgent?: string, 
  ip?: string
): void {
  const logData = {
    timestamp: new Date().toISOString(),
    event: 'SSRF_ATTEMPT_BLOCKED',
    url,
    reason,
    userAgent,
    ip,
    severity: 'HIGH'
  };
  
  // In production, this should go to your security monitoring system
  console.warn('SSRF Attempt Blocked:', logData);
  
  // You could also send to external monitoring service
  // await sendSecurityAlert(logData);
}

// Middleware for Express/Next.js to validate outgoing requests
export function createSSRFMiddleware() {
  return async (req: any, res: any, next: any) => {
    // Override fetch globally for this request
    const originalFetch = global.fetch;
    
    global.fetch = async (url: string | URL, options?: RequestInit) => {
      const urlString = url.toString();
      
      try {
        return await safeFetch(urlString, options);
      } catch (error) {
        // Log the attempt
        logSSRFAttempt(
          urlString,
          error instanceof Error ? error.message : 'Unknown error',
          req.headers['user-agent'],
          req.ip
        );
        throw error;
      }
    };
    
    // Restore original fetch after request
    res.on('finish', () => {
      global.fetch = originalFetch;
    });
    
    next();
  };
}

export default {
  validateURL,
  safeFetch,
  validateWebhookURL,
  sanitizeURL,
  logSSRFAttempt,
  createSSRFMiddleware
};
