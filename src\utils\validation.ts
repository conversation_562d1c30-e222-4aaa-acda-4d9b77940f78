import { z } from 'zod';

// User authentication schemas
export const loginSchema = z.object({
  email: z
    .string()
    .email('Invalid email format')
    .min(1, 'Email is required')
    .max(255, 'Email is too long')
    .transform(email => email.toLowerCase().trim()),
  password: z
    .string()
    .min(1, 'Password is required')
    .max(128, 'Password is too long'),
  rememberMe: z.boolean().optional(),
  csrfToken: z.string().min(1, 'CSRF token is required'),
});

export const registerSchema = z.object({
  email: z
    .string()
    .email('Invalid email format')
    .min(1, 'Email is required')
    .max(255, 'Email is too long')
    .transform(email => email.toLowerCase().trim()),
  password: z
    .string()
    .min(8, 'Password must be at least 8 characters')
    .max(128, 'Password is too long')
    .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
    .regex(/\d/, 'Password must contain at least one number')
    .regex(/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/, 'Password must contain at least one special character'),
  confirmPassword: z.string(),
  firstName: z
    .string()
    .min(1, 'First name is required')
    .max(50, 'First name is too long')
    .regex(/^[a-zA-Z\s]+$/, 'First name can only contain letters and spaces'),
  lastName: z
    .string()
    .min(1, 'Last name is required')
    .max(50, 'Last name is too long')
    .regex(/^[a-zA-Z\s]+$/, 'Last name can only contain letters and spaces'),
  dateOfBirth: z
    .string()
    .regex(/^\d{4}-\d{2}-\d{2}$/, 'Invalid date format (YYYY-MM-DD)')
    .refine(date => {
      const birthDate = new Date(date);
      const today = new Date();
      const age = today.getFullYear() - birthDate.getFullYear();
      return age >= 18 && age <= 120;
    }, 'You must be at least 18 years old'),
  phoneNumber: z
    .string()
    .regex(/^\+?[\d\s\-\(\)]+$/, 'Invalid phone number format')
    .min(10, 'Phone number is too short')
    .max(20, 'Phone number is too long'),
  termsAccepted: z.boolean().refine(val => val === true, 'You must accept the terms and conditions'),
  csrfToken: z.string().min(1, 'CSRF token is required'),
}).refine(data => data.password === data.confirmPassword, {
  message: 'Passwords do not match',
  path: ['confirmPassword'],
});

export const changePasswordSchema = z.object({
  currentPassword: z.string().min(1, 'Current password is required'),
  newPassword: z
    .string()
    .min(8, 'Password must be at least 8 characters')
    .max(128, 'Password is too long')
    .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
    .regex(/\d/, 'Password must contain at least one number')
    .regex(/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/, 'Password must contain at least one special character'),
  confirmNewPassword: z.string(),
  csrfToken: z.string().min(1, 'CSRF token is required'),
}).refine(data => data.newPassword === data.confirmNewPassword, {
  message: 'New passwords do not match',
  path: ['confirmNewPassword'],
});

// Financial transaction schemas
export const transferSchema = z.object({
  fromAccountId: z.string().uuid('Invalid account ID'),
  toAccountId: z.string().uuid('Invalid account ID'),
  amount: z
    .number()
    .positive('Amount must be positive')
    .max(1000000, 'Amount exceeds maximum limit')
    .multipleOf(0.01, 'Amount can only have up to 2 decimal places'),
  description: z
    .string()
    .min(1, 'Description is required')
    .max(255, 'Description is too long')
    .regex(/^[a-zA-Z0-9\s\-_.,!?]+$/, 'Description contains invalid characters'),
  transferType: z.enum(['internal', 'external'], 'Invalid transfer type'),
  scheduledDate: z.string().datetime().optional(),
  csrfToken: z.string().min(1, 'CSRF token is required'),
}).refine(data => data.fromAccountId !== data.toAccountId, {
  message: 'Cannot transfer to the same account',
  path: ['toAccountId'],
});

export const billPaymentSchema = z.object({
  accountId: z.string().uuid('Invalid account ID'),
  payeeId: z.string().uuid('Invalid payee ID'),
  amount: z
    .number()
    .positive('Amount must be positive')
    .max(100000, 'Amount exceeds maximum limit')
    .multipleOf(0.01, 'Amount can only have up to 2 decimal places'),
  dueDate: z.string().datetime('Invalid due date'),
  memo: z
    .string()
    .max(255, 'Memo is too long')
    .regex(/^[a-zA-Z0-9\s\-_.,!?]*$/, 'Memo contains invalid characters')
    .optional(),
  csrfToken: z.string().min(1, 'CSRF token is required'),
});

// Account management schemas
export const createAccountSchema = z.object({
  accountType: z.enum(['checking', 'savings', 'investment'], 'Invalid account type'),
  initialDeposit: z
    .number()
    .min(0, 'Initial deposit cannot be negative')
    .max(1000000, 'Initial deposit exceeds maximum limit')
    .multipleOf(0.01, 'Amount can only have up to 2 decimal places'),
  currency: z.enum(['USD', 'EUR', 'GBP'], 'Invalid currency'),
  csrfToken: z.string().min(1, 'CSRF token is required'),
});

// Profile update schemas
export const updateProfileSchema = z.object({
  firstName: z
    .string()
    .min(1, 'First name is required')
    .max(50, 'First name is too long')
    .regex(/^[a-zA-Z\s]+$/, 'First name can only contain letters and spaces'),
  lastName: z
    .string()
    .min(1, 'Last name is required')
    .max(50, 'Last name is too long')
    .regex(/^[a-zA-Z\s]+$/, 'Last name can only contain letters and spaces'),
  phoneNumber: z
    .string()
    .regex(/^\+?[\d\s\-\(\)]+$/, 'Invalid phone number format')
    .min(10, 'Phone number is too short')
    .max(20, 'Phone number is too long'),
  address: z.object({
    street: z.string().min(1, 'Street address is required').max(100, 'Street address is too long'),
    city: z.string().min(1, 'City is required').max(50, 'City is too long'),
    state: z.string().min(2, 'State is required').max(50, 'State is too long'),
    zipCode: z.string().regex(/^\d{5}(-\d{4})?$/, 'Invalid ZIP code format'),
    country: z.string().min(2, 'Country is required').max(50, 'Country is too long'),
  }),
  csrfToken: z.string().min(1, 'CSRF token is required'),
});

// Search and filter schemas
export const transactionFilterSchema = z.object({
  accountId: z.string().uuid().optional(),
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
  minAmount: z.number().min(0).optional(),
  maxAmount: z.number().min(0).optional(),
  transactionType: z.enum(['debit', 'credit', 'transfer']).optional(),
  status: z.enum(['pending', 'completed', 'failed', 'cancelled']).optional(),
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(20),
}).refine(data => {
  if (data.startDate && data.endDate) {
    return new Date(data.startDate) <= new Date(data.endDate);
  }
  return true;
}, {
  message: 'Start date must be before end date',
  path: ['endDate'],
}).refine(data => {
  if (data.minAmount && data.maxAmount) {
    return data.minAmount <= data.maxAmount;
  }
  return true;
}, {
  message: 'Minimum amount must be less than maximum amount',
  path: ['maxAmount'],
});

// Admin schemas
export const adminUserUpdateSchema = z.object({
  userId: z.string().uuid('Invalid user ID'),
  role: z.enum(['user', 'admin', 'manager'], 'Invalid role'),
  isActive: z.boolean(),
  csrfToken: z.string().min(1, 'CSRF token is required'),
});

// API response schemas
export const apiResponseSchema = z.object({
  success: z.boolean(),
  data: z.any().optional(),
  error: z.string().optional(),
  message: z.string().optional(),
  timestamp: z.string().datetime(),
});

// Type exports
export type LoginInput = z.infer<typeof loginSchema>;
export type RegisterInput = z.infer<typeof registerSchema>;
export type ChangePasswordInput = z.infer<typeof changePasswordSchema>;
export type TransferInput = z.infer<typeof transferSchema>;
export type BillPaymentInput = z.infer<typeof billPaymentSchema>;
export type CreateAccountInput = z.infer<typeof createAccountSchema>;
export type UpdateProfileInput = z.infer<typeof updateProfileSchema>;
export type TransactionFilterInput = z.infer<typeof transactionFilterSchema>;
export type AdminUserUpdateInput = z.infer<typeof adminUserUpdateSchema>;
export type ApiResponse = z.infer<typeof apiResponseSchema>;

// Validation helper function
export const validateInput = <T>(schema: z.ZodSchema<T>, data: unknown): { success: true; data: T } | { success: false; errors: string[] } => {
  try {
    const validatedData = schema.parse(data);
    return { success: true, data: validatedData };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errors = error.errors.map(err => `${err.path.join('.')}: ${err.message}`);
      return { success: false, errors };
    }
    return { success: false, errors: ['Validation failed'] };
  }
};
